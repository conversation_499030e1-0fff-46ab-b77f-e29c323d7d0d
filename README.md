# TTS 听书系统（简化开箱即用版）

一个开箱即用的本地/云端语音合成（TTS）听书应用。

- 单文件应用：双击 exe 即用
- 支持上传 TXT / DOCX / PDF / EPUB
- 智能分章（可自定义正则）
- 本地 TTS（pyttsx3）+ 云端 TTS（edge-tts，可选）
- 批量合成、断点续播、进度保存
- 导出整书音频 ZIP（自动转 MP3，需 pydub）
- 书架管理（列表、打开、删除、导出）

---

## 一、下载可执行文件（推荐给予他人使用）

如果您已经有 exe（开发者已为您打包），用户只需要：

1. 双击 TTSReader.exe
2. 稍等 2 秒，浏览器会自动打开 http://127.0.0.1:8080
3. 直接在页面上传文档、分章、合成与播放

无需安装 Python 或任何环境。

---

## 二、自己本地打包成 exe（开发者/技术同学）

如果您要自己打包 exe（或更新后重新打包），请按以下步骤：

1) 安装 Python 3.10+（Windows）

2) 在项目目录安装依赖：

- 方式 A：全量功能
  - pip install -r requirements.txt

- 方式 B：本地 TTS 最小依赖（不含云端/转码/高级格式）：
  - pip install fastapi uvicorn pyttsx3

3) 可选依赖说明：
- 云端 TTS：edge-tts（pip install edge-tts）
- 导出转 MP3：pydub（pip install pydub）和 FFmpeg（需要安装系统版并加入 PATH）
- 文档解析：python-docx、PyPDF2、ebooklib、beautifulsoup4

4) 一键打包 exe：
- 直接运行 build_exe.ps1（右键用 PowerShell 运行即可），它将执行：
  - pyinstaller --noconfirm --clean --onefile --name TTSReader --add-data "README.md;." tts_reader.py
- 生成的 exe 在 dist/TTSReader.exe

注意：首次运行 exe，系统可能提示安全警告，选择“仍要运行”。

---

## 三、使用说明（界面功能）

1. 上传文档
- 点击“点击上传文档”或将文件拖拽到上传框
- 支持 TXT / DOCX / PDF / EPUB

2. 智能分章
- 点击“智能分章”即可自动识别常见章节（例如“第X章”）
- 如需自定义，可在“章节正则表达式”里输入正则（默认：^(第[一二三四五六七八九十百千万0-9]+章.*?)$）

3. 选择引擎与语速
- 本地引擎（pyttsx3）：无需联网，速度快
- 云端引擎（edge-tts）：音色更自然，需要安装 edge-tts，联网使用
- 调整“语速”滑块，改变合成语速

4. 合成与播放
- 选中章节后，点击“合成并播放”
- 已合成的章节会显示“已缓存”标记，再次播放直接使用缓存

5. 批量合成
- 点击“批量合成所有章节”
- 在“批量合成进度”卡片查看实时状态

6. 进度保存与续播
- 播放过程中会自动定期保存进度（章节与时间点）
- 打开书后会显示上次播放位置，点击“继续播放”即可从书签处恢复

7. 导出整书音频
- 书籍详情页点击“导出音频”（或在书架卡片点击“导出”）
- 将按章节序号打包为 ZIP，若检测到需要转 MP3 且已安装 pydub+FFmpeg，会自动转 mp3 再打包

8. 书架管理
- 打开应用会展示“我的书架”：所有导入的书籍
- 每本书显示章节数、缓存数、是否有播放记录
- 支持“打开”、“导出”、“删除”操作

---

## 四、常见问题（FAQ）

1) 浏览器没有自动打开？
- 手动打开 http://127.0.0.1:8080

2) 云端 TTS 语音不可选？
- 需要安装 edge-tts：pip install edge-tts
- 打包 exe 时也需包含该依赖

3) 导出 mp3 失败或导出后不是 mp3？
- 需要安装 pydub：pip install pydub
- 需要安装 FFmpeg 并加入系统 PATH（Windows 可以安装 ffmpeg 并在系统环境变量里添加其 bin 路径）

4) PDF/EPUB/DOCX 解析失败？
- 请安装对应依赖：python-docx、PyPDF2、ebooklib、beautifulsoup4
- 极少数复杂 PDF/EPUB 解析可能部分遗漏文本，可尝试先导出为 txt 再导入

5) 合成速度慢/卡顿？
- 本地 TTS：减少语速或关闭其他大型程序
- 云端 TTS：检查网络状况，并避免文本过长（合理分章）

6) exe 文件运行时被拦截？
- Windows 可能弹出安全提示，选择“更多信息”>“仍要运行”
- 如需给他人分发，建议使用已签名证书进行代码签名

---

## 五、项目结构

- tts_reader.py           单文件应用（FastAPI + 前端 UI）
- README.md               使用文档（本文件）
- requirements.txt        依赖列表
- build_exe.ps1           一键打包脚本（PyInstaller）

---

## 六、开发者说明

- 运行源码：
  - python tts_reader.py
  - 打开 http://127.0.0.1:8080

- 关键技术：
  - FastAPI 提供 HTTP 接口与静态页面
  - pyttsx3 提供本地语音合成
  - edge-tts 提供云端语音合成
  - SQLite 存储书籍、章节与播放进度
  - pydub+FFmpeg 进行导出 mp3 转码

- 数据存储：
  - 缓存与数据库默认在系统临时目录（例如 %TEMP%\tts_reader_cache）
  - 删除书籍会删除其缓存音频文件

---

## 七、许可证

本项目可自由学习与使用，若用于商业用途请自行评估相关 TTS 服务条款与授权。

