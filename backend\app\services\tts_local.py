# -*- coding: utf-8 -*-
"""本地TTS（pyttsx3）封装
- MVP阶段：仅支持语速(rate)，音调(pitch)/停顿(pause)暂不生效
- 输出：保存为WAV文件，由上层选择是否转MP3
"""
from __future__ import annotations
import os
from pathlib import Path


def synthesize_wav(text: str, wav_path: Path, rate: int | None = None) -> None:
    """使用 pyttsx3 合成到 WAV 文件。
    - 如果未安装 pyttsx3，将抛出 ImportError，由上层捕获并提示用户安装
    - rate: 语速（大于0的整数，pyttsx3默认约200）
    """
    import pyttsx3  # 延迟导入，避免未安装时模块级导入即失败

    wav_path.parent.mkdir(parents=True, exist_ok=True)
    engine = pyttsx3.init()
    try:
        if rate and isinstance(rate, int) and rate > 0:
            engine.setProperty('rate', rate)
        # 可选：音量 0.0~1.0（保留默认）
        # engine.setProperty('volume', 1.0)
        engine.save_to_file(text, wav_path.as_posix())
        engine.runAndWait()
    finally:
        engine.stop()

