#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TTS 听书系统（简化开箱即用版）
- 单文件 FastAPI 应用，内置前端页面
- 支持 TXT / DOCX / PDF / EPUB 导入与智能分章
- 本地 TTS（pyttsx3）与可选云端 TTS（edge-tts）
- 批量合成、进度保存、ZIP 导出

运行：python tts_reader.py  后自动打开浏览器 http://127.0.0.1:8080
打包：见 build_exe.ps1 或 README.md
"""
import asyncio
import json
import os
import queue
import re
import sqlite3
import tempfile
import threading
import time
import uuid
import webbrowser
from contextlib import contextmanager
from datetime import datetime
from pathlib import Path
from typing import List, Dict

from fastapi import FastAPI, HTTPException, UploadFile, File, Query
from fastapi.responses import HTMLResponse, FileResponse
from fastapi.staticfiles import StaticFiles
import uvicorn
import pyttsx3

# 可选：云端 TTS（未安装时自动降级）
try:
    import edge_tts  # type: ignore
    HAS_EDGE_TTS = True
except Exception:
    HAS_EDGE_TTS = False

# 可选：导出 mp3 转码
try:
    from pydub import AudioSegment  # type: ignore
    HAS_PYDUB = True
except Exception:
    HAS_PYDUB = False

# 可选：文档解析
ttry = []
try:
    import docx  # type: ignore
    HAS_DOCX = True
except Exception:
    HAS_DOCX = False
try:
    import PyPDF2  # type: ignore
    HAS_PDF = True
except Exception:
    HAS_PDF = False
try:
    import ebooklib  # type: ignore
    from ebooklib import epub  # type: ignore
    from bs4 import BeautifulSoup  # type: ignore
    HAS_EPUB = True
except Exception:
    HAS_EPUB = False


class TTSApp:
    def __init__(self):
        self.app = FastAPI(title="TTS 听书系统（简化版）")
        self.cache_dir = Path(tempfile.gettempdir()) / "tts_reader_cache"
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        self.db_path = self.cache_dir / "tts_reader.db"

        # 批量合成
        self.task_queue: "queue.Queue[dict]" = queue.Queue()
        self.batch_tasks: Dict[str, dict] = {}
        self._start_worker()

        self._init_db()
        self._routes()
        self.app.mount("/audio-files", StaticFiles(directory=str(self.cache_dir)), name="audio-files")

        # 自动打开浏览器
        threading.Timer(1.5, lambda: webbrowser.open("http://127.0.0.1:8080")).start()

    @contextmanager
    def db(self):
        conn = sqlite3.connect(str(self.db_path), check_same_thread=False)
        conn.row_factory = sqlite3.Row
        try:
            yield conn
            conn.commit()
        except Exception:
            conn.rollback()
            raise
        finally:
            conn.close()

    def _init_db(self):
        with self.db() as conn:
            conn.executescript(
                """
                CREATE TABLE IF NOT EXISTS books (
                  id INTEGER PRIMARY KEY,
                  title TEXT,
                  file_path TEXT,
                  encoding TEXT,
                  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                );
                CREATE TABLE IF NOT EXISTS chapters (
                  id INTEGER PRIMARY KEY,
                  book_id INTEGER,
                  idx INTEGER,
                  title TEXT,
                  start_offset INTEGER,
                  end_offset INTEGER,
                  cached_path TEXT,
                  tts_engine TEXT DEFAULT 'local'
                );
                CREATE TABLE IF NOT EXISTS playback_progress (
                  book_id INTEGER PRIMARY KEY,
                  chapter_id INTEGER,
                  position REAL DEFAULT 0,
                  last_played TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                );
                """
            )

    # ---------- 文本与分章 ----------
    def detect_encoding(self, file_path: Path) -> str:
        for enc in ["utf-8", "gbk", "gb2312", "utf-16"]:
            try:
                with open(file_path, "r", encoding=enc) as f:
                    f.read(1024)
                return enc
            except Exception:
                pass
        return "utf-8"

    def extract_text(self, file_path: Path, ext: str) -> str:
        try:
            if ext == "txt":
                enc = self.detect_encoding(file_path)
                return file_path.read_text(encoding=enc)
            if ext == "docx" and HAS_DOCX:
                d = docx.Document(file_path)
                return "\n".join(p.text for p in d.paragraphs)
            if ext == "pdf" and HAS_PDF:
                text = ""
                with open(file_path, "rb") as f:
                    reader = PyPDF2.PdfReader(f)
                    for p in reader.pages:
                        t = p.extract_text() or ""
                        text += t + "\n"
                return text
            if ext == "epub" and HAS_EPUB:
                book = epub.read_epub(file_path)
                text = ""
                for item in book.get_items():
                    if item.get_type() == ebooklib.ITEM_DOCUMENT:
                        soup = BeautifulSoup(item.get_content(), "html.parser")
                        text += soup.get_text() + "\n"
                return text
        except Exception as e:
            raise ValueError(f"解析失败: {e}")
        raise ValueError(f"不支持的格式或缺少依赖: {ext}")

    def split_chapters_regex(self, text: str, pattern: str | None) -> List[Dict]:
        """基于正则的分章"""
        if not pattern:
            pattern = r"^(第[一二三四五六七八九十百千万0-9]+[章节回卷部集].*?)$"
        regex = re.compile(pattern, re.MULTILINE)
        matches = list(regex.finditer(text))
        if not matches:
            return []
        chapters: List[Dict] = []
        for i, m in enumerate(matches):
            start = m.start()
            end = matches[i + 1].start() if i + 1 < len(matches) else len(text)
            title = m.group(1).strip()
            chapters.append({"title": title, "start_offset": start, "end_offset": end})
        return chapters

    def _looks_like_heading(self, line: str) -> bool:
        line = line.strip()
        if not line:
            return False
        # 常见标题特征：第X章/回/卷/集，或全角/半角数字+标点，或长度较短
        if re.match(r"^(第?[一二三四五六七八九十百千万0-9]+\s*[章节回卷部集]).*", line):
            return True
        if len(line) <= 18 and (re.match(r"^[\u4e00-\u9fa5A-Za-z0-9\s，、。！？.!?—-]+$", line) is not None):
            # 短行，且大多为文字/标点
            return True
        return False

    def split_chapters_content(self, text: str, target_len: int = 1800, min_len: int = 1200, max_len: int = 3000) -> List[Dict]:
        """基于内容的智能分段：
        - 先按空行/换行切段
        - 命中标题样式的行会强制切分
        - 聚合段落直到接近目标长度，优先在句末（。！？）处截断
        - 为无标题段生成“第N段: 片段摘要”
        """
        # 预处理换行，统一为\n
        text = text.replace("\r\n", "\n").replace("\r", "\n")
        lines = text.split("\n")
        # 先切成段落块
        blocks: List[str] = []
        buf: List[str] = []
        for ln in lines:
            if ln.strip() == "":
                if buf:
                    blocks.append("\n".join(buf).strip())
                    buf = []
            else:
                buf.append(ln)
        if buf:
            blocks.append("\n".join(buf).strip())

        # 聚合为章节
        chapters: List[Dict] = []
        cursor = 0
        acc = ""
        acc_start = 0
        pending_title: str | None = None

        def emit_segment(seg: str, start_offset: int, title_hint: str | None):
            if not seg:
                return
            end_offset = start_offset + len(seg)
            # 生成标题
            title = None
            if title_hint and self._looks_like_heading(title_hint):
                title = title_hint.strip()
            if not title:
                # 取首句作为摘要
                m = re.search(r"(.{6,24}?)[。！？.!?]", seg)
                snippet = m.group(1) if m else seg[:20]
                snippet = re.sub(r"\s+", " ", snippet)
                title = f"第{len(chapters)+1}段: {snippet}"
            chapters.append({
                "title": title,
                "start_offset": start_offset,
                "end_offset": end_offset,
            })

        i = 0
        while i < len(blocks):
            block = blocks[i]
            block_start_in_text = text.find(block, cursor)
            if block_start_in_text == -1:
                # 找不到时从游标处拼接
                block_start_in_text = cursor
            # 如果这是标题风格的独立行，先把已有累积发射
            if self._looks_like_heading(block) and len(block) <= 40:
                if acc and len(acc) >= min_len:
                    emit_segment(acc, acc_start, pending_title)
                    cursor = acc_start + len(acc)
                    acc = ""
                    pending_title = block
                    i += 1
                    continue
                # 没有足够内容，作为下一段标题使用
                if not acc:
                    pending_title = block
                    acc_start = block_start_in_text + len(block)
                    cursor = acc_start
                    i += 1
                    continue
            # 累积块
            if not acc:
                acc_start = block_start_in_text
            acc += (("\n\n" if acc else "") + block)
            # 达到最大/目标长度，尝试在句末断开
            if len(acc) >= max_len or (len(acc) >= target_len and self._looks_like_heading(block)):
                cut = len(acc)
                # 优先在最近的句号类标点处断
                for p in ["。", "！", "？", ".", "!", "?", "\n\n"]:
                    pos = acc.rfind(p)
                    if pos != -1 and pos > min_len:
                        cut = pos + len(p)
                        break
                seg = acc[:cut].strip()
                emit_segment(seg, acc_start, pending_title)
                cursor = acc_start + len(seg)
                # 剩余放回 acc
                acc = acc[cut:].lstrip()
                pending_title = None
            i += 1
        # 发射尾段
        if acc.strip():
            emit_segment(acc.strip(), acc_start, pending_title)

        # 若仍然没有切分，则按固定步长兜底
        if not chapters:
            step = target_len
            chapters = [
                {"title": f"第{i+1}段", "start_offset": i, "end_offset": min(i+step, len(text))}
                for i in range(0, len(text), step)
            ]
        return chapters

    def split_chapters(self, text: str, pattern: str | None, strategy: str = "auto", target_len: int = 1800) -> List[Dict]:
        """统一入口：strategy=auto|regex|content"""
        strategy = (strategy or "auto").lower()
        if strategy == "regex":
            chs = self.split_chapters_regex(text, pattern)
            return chs if chs else self.split_chapters_content(text, target_len)
        if strategy == "content":
            return self.split_chapters_content(text, target_len)
        # auto：先试正则，不理想则内容分段
        chs = self.split_chapters_regex(text, pattern)
        if not chs or len(chs) <= 1 or (len(text) // max(1, len(chs)) < 800):
            return self.split_chapters_content(text, target_len)
        return chs

    # ---------- 合成 ----------
    def tts_local(self, text: str, out_path: Path, rate: int = 200) -> bool:
        try:
            engine = pyttsx3.init()
            engine.setProperty("rate", rate)
            engine.setProperty("volume", 1.0)
            out_path.parent.mkdir(parents=True, exist_ok=True)
            if out_path.exists():
                out_path.unlink()
            engine.save_to_file(text, str(out_path))
            engine.runAndWait()
            return out_path.exists() and out_path.stat().st_size > 100
        except Exception:
            return False

    async def tts_cloud(self, text: str, out_path: Path, voice: str = "zh-CN-XiaoxiaoNeural", rate: str = "+0%") -> bool:
        if not HAS_EDGE_TTS:
            return False
        try:
            out_path.parent.mkdir(parents=True, exist_ok=True)
            communicate = edge_tts.Communicate(text, voice=voice, rate=rate)
            await communicate.save(str(out_path))
            return out_path.exists() and out_path.stat().st_size > 100
        except Exception:
            return False

    def _start_worker(self):
        def worker():
            while True:
                try:
                    task = self.task_queue.get(timeout=1)
                except queue.Empty:
                    continue
                if task is None:
                    break
                task_id = task["id"]
                self.batch_tasks[task_id]["status"] = "processing"
                ok = False
                try:
                    if task["engine"] == "cloud":
                        path = self.cache_dir / f"ch_{task['chapter_id']}_cloud.mp3"
                        loop = asyncio.new_event_loop()
                        asyncio.set_event_loop(loop)
                        ok = loop.run_until_complete(self.tts_cloud(task["text"], path, task.get("voice", "zh-CN-XiaoxiaoNeural")))
                    else:
                        path = self.cache_dir / f"ch_{task['chapter_id']}.wav"
                        ok = self.tts_local(task["text"], path, task.get("rate", 200))
                    if ok:
                        with self.db() as conn:
                            conn.execute(
                                "UPDATE chapters SET cached_path = ?, tts_engine = ? WHERE id = ?",
                                (str(path), task["engine"], task["chapter_id"]),
                            )
                except Exception:
                    ok = False
                self.batch_tasks[task_id]["status"] = "completed" if ok else "failed"
                self.batch_tasks[task_id]["completed_at"] = datetime.now().isoformat()
        threading.Thread(target=worker, daemon=True).start()

    # ---------- 路由 ----------
    def _routes(self):
        app = self.app

        @app.get("/", response_class=HTMLResponse)
        def index():
            return self._html()

        @app.post("/import")
        async def import_book(file: UploadFile = File(...)):
            ext = (file.filename.split(".")[-1] or "").lower()
            if ext not in ["txt", "docx", "pdf", "epub"]:
                raise HTTPException(400, detail="仅支持 txt/docx/pdf/epub")
            path = self.cache_dir / f"book_{int(time.time())}.{ext}"
            with open(path, "wb") as f:
                f.write(await file.read())
            try:
                text = self.extract_text(path, ext)
            except Exception as e:
                raise HTTPException(400, detail=str(e))
            if not text.strip():
                raise HTTPException(400, detail="文件内容为空")
            with self.db() as conn:
                cur = conn.execute(
                    "INSERT INTO books(title, file_path, encoding) VALUES(?,?,?)",
                    (file.filename, str(path), ext),
                )
                book_id = cur.lastrowid
            return {"bookId": book_id, "title": file.filename}

        @app.post("/preprocess/{book_id}")
        def preprocess(book_id: int, regex_pattern: str | None = None, strategy: str = "auto", target_len: int = 1800):
            with self.db() as conn:
                b = conn.execute("SELECT * FROM books WHERE id=?", (book_id,)).fetchone()
                if not b:
                    raise HTTPException(404, detail="书籍不存在")
                text = self.extract_text(Path(b["file_path"]), b["encoding"])  # type: ignore
                chapters = self.split_chapters(text, regex_pattern, strategy=strategy, target_len=target_len)
                conn.execute("DELETE FROM chapters WHERE book_id=?", (book_id,))
                for i, ch in enumerate(chapters, 1):
                    conn.execute(
                        "INSERT INTO chapters(book_id, idx, title, start_offset, end_offset) VALUES(?,?,?,?,?)",
                        (book_id, i, ch["title"], ch["start_offset"], ch["end_offset"]),
                    )
                return {"bookId": book_id, "chapters": len(chapters)}

        @app.get("/chapters/{book_id}")
        def list_chapters(book_id: int):
            with self.db() as conn:
                rows = conn.execute(
                    "SELECT id, idx, title, cached_path FROM chapters WHERE book_id=? ORDER BY idx",
                    (book_id,),
                ).fetchall()
            return [
                {"id": r["id"], "idx": r["idx"], "title": r["title"], "cached": bool(r["cached_path"]) }
                for r in rows
            ]

        @app.post("/synthesize/{book_id}/{chapter_id}")
        async def synthesize(book_id: int, chapter_id: int, engine: str = Query("local"), voice: str = Query("zh-CN-XiaoxiaoNeural"), rate: int = Query(200)):
            with self.db() as conn:
                b = conn.execute("SELECT * FROM books WHERE id=?", (book_id,)).fetchone()
                c = conn.execute("SELECT * FROM chapters WHERE id=? AND book_id=?", (chapter_id, book_id)).fetchone()
                if not b or not c:
                    raise HTTPException(404, detail="书籍或章节不存在")
                text = self.extract_text(Path(b["file_path"]), b["encoding"])  # type: ignore
                seg = text[c["start_offset"]: c["end_offset"]]
            if not seg.strip():
                raise HTTPException(400, detail="章节内容为空")
            if engine == "cloud" and HAS_EDGE_TTS:
                out = self.cache_dir / f"ch_{chapter_id}_cloud.mp3"
                ok = await self.tts_cloud(seg, out, voice=voice)
            else:
                out = self.cache_dir / f"ch_{chapter_id}.wav"
                ok = self.tts_local(seg, out, rate=rate)
            if not ok:
                raise HTTPException(500, detail="合成失败")
            with self.db() as conn:
                conn.execute("UPDATE chapters SET cached_path=?, tts_engine=? WHERE id=?", (str(out), engine, chapter_id))
            return {"audioPath": f"/audio-files/{out.name}", "cached": False}

        @app.get("/voices")
        def voices():
            return {
                "local": {"available": True, "voices": [{"name": "系统默认", "value": "default"}]},
                "cloud": {"available": HAS_EDGE_TTS, "voices": [
                    {"name": "晓晓", "value": "zh-CN-XiaoxiaoNeural", "gender": "女"},
                    {"name": "云希", "value": "zh-CN-YunxiNeural", "gender": "男"},
                    {"name": "晓伊", "value": "zh-CN-XiaoyiNeural", "gender": "女"},
                    {"name": "云扬", "value": "zh-CN-YunyangNeural", "gender": "男"},
                ]}
            }

        @app.get("/audio/{book_id}/{chapter_id}")
        def get_audio(book_id: int, chapter_id: int):
            with self.db() as conn:
                c = conn.execute("SELECT cached_path FROM chapters WHERE id=? AND book_id=?", (chapter_id, book_id)).fetchone()
            if not c or not c["cached_path"]:
                raise HTTPException(404, detail="音频不存在")
            path = Path(c["cached_path"])
            if not path.exists():
                raise HTTPException(404, detail="文件缺失")
            media = "audio/mpeg" if path.suffix.lower() == ".mp3" else "audio/wav"
            return FileResponse(path, media_type=media)

        @app.post("/batch/synthesize/{book_id}")
        def batch(book_id: int, engine: str = Query("local"), voice: str = Query("zh-CN-XiaoxiaoNeural")):
            with self.db() as conn:
                b = conn.execute("SELECT * FROM books WHERE id=?", (book_id,)).fetchone()
                if not b:
                    raise HTTPException(404, detail="书籍不存在")
                chapters = conn.execute("SELECT * FROM chapters WHERE book_id=? ORDER BY idx", (book_id,)).fetchall()
                if not chapters:
                    raise HTTPException(404, detail="没有章节")
                text = self.extract_text(Path(b["file_path"]), b["encoding"])  # type: ignore
                added = 0
                for ch in chapters:
                    if ch["cached_path"] and Path(ch["cached_path"]).exists():
                        continue
                    seg = text[ch["start_offset"]: ch["end_offset"]]
                    task_id = str(uuid.uuid4())
                    self.batch_tasks[task_id] = {"status": "pending", "chapter": ch["title"], "created_at": datetime.now().isoformat()}
                    self.task_queue.put({"id": task_id, "book_id": book_id, "chapter_id": ch["id"], "text": seg, "engine": engine, "voice": voice})
                    added += 1
            return {"message": f"已添加 {added} 个任务", "count": added}

        @app.get("/batch/status")
        def batch_status():
            counts = {"pending": 0, "processing": 0, "completed": 0, "failed": 0}
            for t in self.batch_tasks.values():
                counts[t["status"]] = counts.get(t["status"], 0) + 1
            counts["total"] = sum(counts.values())
            return counts

        @app.post("/progress/save")
        def save_progress(book_id: int = Query(...), chapter_id: int = Query(...), position: float = Query(...)):
            with self.db() as conn:
                exist = conn.execute("SELECT 1 FROM playback_progress WHERE book_id=?", (book_id,)).fetchone()
                if exist:
                    conn.execute("UPDATE playback_progress SET chapter_id=?, position=?, last_played=CURRENT_TIMESTAMP WHERE book_id=?", (chapter_id, position, book_id))
                else:
                    conn.execute("INSERT INTO playback_progress(book_id, chapter_id, position) VALUES(?,?,?)", (book_id, chapter_id, position))
            return {"ok": True}

        @app.get("/progress/{book_id}")
        def get_progress(book_id: int):
            with self.db() as conn:
                r = conn.execute(
                    "SELECT p.*, c.title chapter_title, c.idx chapter_idx FROM playback_progress p LEFT JOIN chapters c ON c.id=p.chapter_id WHERE p.book_id=?",
                    (book_id,),
                ).fetchone()
            if not r:
                return None
            return {
                "bookId": r["book_id"],
                "chapterId": r["chapter_id"],
                "position": r["position"],
                "chapterTitle": r["chapter_title"],
                "chapterIdx": r["chapter_idx"],
                "lastPlayed": r["last_played"],
            }

        @app.get("/books")
        def books():
            with self.db() as conn:
                rows = conn.execute(
                    """
                    SELECT b.*, COUNT(c.id) chapter_count,
                           SUM(CASE WHEN c.cached_path IS NOT NULL THEN 1 ELSE 0 END) cached_count
                    FROM books b LEFT JOIN chapters c ON b.id=c.book_id
                    GROUP BY b.id ORDER BY b.created_at DESC
                    """
                ).fetchall()
                result = []
                for r in rows:
                    prog = conn.execute("SELECT 1 FROM playback_progress WHERE book_id=?", (r["id"],)).fetchone()
                    result.append({
                        "id": r["id"],
                        "title": r["title"],
                        "chapterCount": r["chapter_count"] or 0,
                        "cachedCount": r["cached_count"] or 0,
                        "hasProgress": bool(prog),
                        "createdAt": r["created_at"],
                    })
            return result

        @app.delete("/books/{book_id}")
        def delete_book(book_id: int):
            with self.db() as conn:
                rows = conn.execute("SELECT cached_path FROM chapters WHERE book_id=? AND cached_path IS NOT NULL", (book_id,)).fetchall()
                for r in rows:
                    p = Path(r["cached_path"])
                    if p.exists():
                        try:
                            p.unlink()
                        except Exception:
                            pass
                conn.execute("DELETE FROM playback_progress WHERE book_id=?", (book_id,))
                conn.execute("DELETE FROM chapters WHERE book_id=?", (book_id,))
                conn.execute("DELETE FROM books WHERE id=?", (book_id,))
            return {"ok": True}

        @app.get("/export/{book_id}")
        def export_book(book_id: int, format: str = Query("mp3")):
            import zipfile
            from io import BytesIO
            from fastapi.responses import StreamingResponse
            with self.db() as conn:
                b = conn.execute("SELECT * FROM books WHERE id=?", (book_id,)).fetchone()
                if not b:
                    raise HTTPException(404, detail="书籍不存在")
                chapters = conn.execute("SELECT * FROM chapters WHERE book_id=? AND cached_path IS NOT NULL ORDER BY idx", (book_id,)).fetchall()
                if not chapters:
                    raise HTTPException(400, detail="没有可导出的音频")
                buf = BytesIO()
                try:
                    with zipfile.ZipFile(buf, "w", zipfile.ZIP_DEFLATED) as z:
                        for ch in chapters:
                            ap = Path(ch["cached_path"])
                            if not ap.exists():
                                continue
                            name = f"{ch['idx']:03d}_{ch['title']}{ap.suffix}"
                            safe = re.sub(r"[\u003c\u003e:\"/\\|?*]", "_", name)
                            if format.lower() == "mp3" and ap.suffix.lower() != ".mp3" and HAS_PYDUB:
                                try:
                                    audio = AudioSegment.from_file(str(ap))
                                    tmp = self.cache_dir / f"tmp_{ch['id']}.mp3"
                                    audio.export(str(tmp), format="mp3")
                                    z.write(str(tmp), safe.replace(ap.suffix, ".mp3"))
                                    try:
                                        tmp.unlink()
                                    except Exception:
                                        pass
                                except Exception:
                                    # 转码失败时，回退为直接打包原文件
                                    z.write(str(ap), safe)
                            else:
                                z.write(str(ap), safe)
                except Exception:
                    # 如果打包过程中出现任何异常，返回一个包含说明的空ZIP而不是500
                    buf = BytesIO()
                    with zipfile.ZipFile(buf, "w", zipfile.ZIP_DEFLATED) as z:
                        info = "导出过程中发生错误，已回退为空压缩包。请检查音频文件是否存在或文件名是否包含特殊字符。"
                        z.writestr("README.txt", info)
                buf.seek(0)
                dl = re.sub(r"[\u003c\u003e:\"/\\|?*]", "_", f"{b['title']}_音频.zip")
                return StreamingResponse(buf, media_type="application/zip", headers={"Content-Disposition": f"attachment; filename*=UTF-8''{dl}"})

    # ---------- 页面 ----------
    def _html(self) -> str:
        return """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta charset="utf-8" />
<meta name="viewport" content="width=device-width, initial-scale=1" />
<title>TTS 听书系统（简化版）</title>
<style>
body{font-family:-apple-system,Segoe UI,Roboto,Helvetica,Arial,sans-serif;background:#f3f5ff;margin:0}
.container{max-width:1100px;margin:30px auto;background:#fff;border-radius:14px;box-shadow:0 12px 40px rgba(0,0,0,.12);overflow:hidden}
.header{background:linear-gradient(135deg,#667eea,#764ba2);color:#fff;padding:24px 30px}
.header h1{margin:0;font-size:22px}
.main{padding:24px 30px}
.upload{border:2px dashed #667eea;padding:36px;text-align:center;border-radius:12px;background:#f8f9ff;cursor:pointer}
.btn{padding:10px 16px;border:0;border-radius:8px;cursor:pointer;font-weight:600}
.btn-primary{background:#667eea;color:#fff}
.btn-info{background:#17a2b8;color:#fff}
.btn-danger{background:#e74c3c;color:#fff}
.grid{display:grid;grid-template-columns:1fr 2fr;gap:24px;margin-top:20px}
.list{max-height:480px;overflow:auto;border:1px solid #eee;border-radius:8px;background:#fafafa}
.item{padding:12px 14px;border-bottom:1px solid #eee;display:flex;justify-content:space-between;align-items:center}
.badge{background:#4caf50;color:#fff;border-radius:12px;padding:2px 8px;font-size:12px}
.small{font-size:12px;color:#666}
.bookbar{background:#f8f9ff;padding:16px;border-radius:10px;margin-bottom:16px}
.cardwrap{display:grid;grid-template-columns:repeat(auto-fill,minmax(260px,1fr));gap:12px}
.card{border:1px solid #eee;border-radius:8px;padding:12px;background:#fff}
</style>
</head>
<body>
<div class="container">
  <div class="header">
    <h1>🎧 TTS 听书系统（简化版）</h1>
    <div class="small">支持 TXT / DOCX / PDF / EPUB | 本地与云端 TTS | 批量合成 | 导出 ZIP</div>
  </div>
  <div class="main">

    <div id="bookshelf" class="bookbar" style="display:none">
      <b>📚 我的书架</b>
      <div id="bookList" class="cardwrap" style="margin-top:10px"></div>
    </div>

    <div class="upload" onclick="document.getElementById('file').click()">
      <h3>📁 点击选择或拖入文件</h3>
      <div class="small">支持 TXT / DOCX / PDF / EPUB</div>
      <input id="file" type="file" accept=".txt,.docx,.pdf,.epub" style="display:none" />
    </div>

    <div id="status" class="small" style="display:none;margin-top:10px"></div>

    <div id="bookInfo" style="display:none;margin-top:16px">
      <h3>📖 <span id="bookTitle"></span></h3>
      <div style="display:flex;gap:8px;align-items:center;margin:10px 0;flex-wrap:wrap">
        <select id="splitStrategy" style="padding:8px;border:1px solid #eee;border-radius:8px">
          <option value="auto" selected>分段策略：自动</option>
          <option value="regex">分段策略：正则</option>
          <option value="content">分段策略：按内容</option>
        </select>
        <input id="targetLen" type="number" value="1800" min="600" max="4000" step="100" title="目标长度(字符)" style="width:140px;padding:8px;border:1px solid #eee;border-radius:8px" />
        <input id="regex" placeholder="章节正则（可选）" style="flex:1;min-width:200px;padding:8px;border:1px solid #eee;border-radius:8px" />
        <button class="btn btn-primary" onclick="preprocess()">🔍 智能分章</button>
        <button class="btn btn-info" onclick="exportBook()">📥 导出音频</button>
      </div>
    </div>

    <div id="engineBox" style="display:none;margin-top:10px;background:#f8f9ff;padding:12px;border-radius:10px">
      <b>🎯 引擎</b>
      <label style="margin-left:10px"><input type="radio" name="engine" value="local" checked onchange="engineChanged()"> 本地</label>
      <label id="cloudOpt" style="margin-left:10px"><input type="radio" name="engine" value="cloud" onchange="engineChanged()"> 云端</label>
      <div id="voices" style="display:none;margin-top:8px"></div>
      <div style="margin-top:10px">
        <label>语速: <span id="rateVal">200</span></label>
        <input id="rate" type="range" min="100" max="300" value="200" oninput="document.getElementById('rateVal').innerText=this.value" />
        <button class="btn" style="margin-left:8px;background:#f39c12;color:#fff" onclick="batch()">⚡ 批量合成</button>
      </div>
    </div>

    <div id="batchPanel" class="bookbar" style="display:none;margin-top:12px">
      <b>📊 批量进度</b>
      <div id="batchState" class="small" style="margin-top:6px">-</div>
    </div>

    <div id="mainUI" class="grid" style="display:none">
      <div>
        <h3>📑 章节</h3>
        <div id="chapters" class="list"></div>
      </div>
      <div>
        <h3>🎵 播放器</h3>
        <div id="current" style="margin:8px 0">请选择章节</div>
        <div id="bookmarkBox" class="small" style="display:none;margin:8px 0"></div>
        <audio id="player" controls style="display:none;width:100%"></audio>
        <div style="margin-top:8px">
          <button id="playBtn" class="btn btn-primary" disabled onclick="synthPlay()">🎤 合成并播放</button>
          <button id="pauseBtn" class="btn" onclick="togglePlay()" disabled>⏸️ 暂停</button>
        </div>
      </div>
    </div>

  </div>
</div>
<script>
let bookId=null, chapterId=null, engine='local', voice='zh-CN-XiaoxiaoNeural';
let avail={};

window.onload=async()=>{ await loadVoices(); await loadBooks(); document.getElementById('file').addEventListener('change', e=> upload(e.target.files[0])); }

document.addEventListener('dragover',e=>e.preventDefault());
document.addEventListener('drop',e=>{e.preventDefault(); if(e.dataTransfer.files.length>0) upload(e.dataTransfer.files[0]);});

function msg(t,kind='info'){const s=document.getElementById('status'); s.innerText=t; s.style.display='block'; s.style.color= kind==='error'?'#c0392b': (kind==='success'?'#2e7d32':'#1976d2'); setTimeout(()=>s.style.display='none', 2500);}

async function loadVoices(){ try{ const r=await fetch('/voices'); avail=await r.json(); if(!avail.cloud.available){document.getElementById('cloudOpt').style.opacity='.5'; document.getElementById('cloudOpt').title='未安装 edge-tts';}}
catch(e){} }

function engineChanged(){ engine=document.querySelector('input[name="engine"]:checked').value; const v=document.getElementById('voices'); if(engine==='cloud' && avail.cloud.available){ v.style.display='block'; v.innerHTML=avail.cloud.voices.map(x=>`<label style='margin-right:8px'><input type='radio' name='v' ${x.value===voice?'checked':''} onclick="voice='${x.value}'"> ${x.name}</label>`).join(''); } else { v.style.display='none'; } }

async function upload(file){
  if(!file) return;
  msg('正在导入...');
  try{
    const fd=new FormData();
    fd.append('file',file);
    const r=await fetch('/import',{method:'POST',body:fd});
    let x=null;
    try{ x=await r.json(); }
    catch(e){ const txt=await r.text().catch(()=>'' ); msg(`导入失败(${r.status}): ${txt.slice(0,120)}`,'error'); return; }
    if(!r.ok){ msg(x && x.detail ? x.detail : '导入失败','error'); return; }
    bookId=x.bookId;
    document.getElementById('bookTitle').innerText=x.title;
    document.getElementById('bookInfo').style.display='block';
    await loadBooks();
    msg('导入成功','success');
  }catch(err){ msg('导入异常: '+(err&&err.message?err.message:err),'error'); }
}

async function preprocess(){ if(!bookId) return; msg('正在分章...'); const re=document.getElementById('regex').value; const strategy=document.getElementById('splitStrategy').value; const targetLen=document.getElementById('targetLen').value||1800; const params=new URLSearchParams({strategy:strategy,target_len:targetLen}); if(re) params.append('regex_pattern', re); const url=`/preprocess/${bookId}?${params}`; const r=await fetch(url,{method:'POST'}); const x=await r.json(); if(!r.ok){ msg(x.detail||'失败','error'); return;} msg(`分章完成，共 ${x.chapters} 章`,'success'); await loadChapters(); document.getElementById('engineBox').style.display='block'; document.getElementById('mainUI').style.display='grid'; await loadProgress(); }

async function loadChapters(){ const r=await fetch(`/chapters/${bookId}`); const list=await r.json(); const box=document.getElementById('chapters'); box.innerHTML=list.map(ch=>`<div class='item' data-id='${ch.id}' onclick='pick(${ch.id},${JSON.stringify(ch.title)})'><span>${ch.idx}. ${ch.title}</span>${ch.cached?"<span class='badge'>已缓存</span>":''}</div>`).join(''); }

function pick(id,title){ chapterId=id; document.getElementById('current').innerText='当前章节：'+title; document.getElementById('playBtn').disabled=false; const p=document.getElementById('player'); if(!p.paused){p.pause(); p.currentTime=0;} }

async function synthPlay(){ if(!bookId||!chapterId) return; msg('正在合成...'); const rate=document.getElementById('rate').value; const q=new URLSearchParams({engine,voice,rate}); const r=await fetch(`/synthesize/${bookId}/${chapterId}?`+q,{method:'POST'}); const x=await r.json(); if(!r.ok){ msg(x.detail||'失败','error'); return;} const p=document.getElementById('player'); p.src=`/audio/${bookId}/${chapterId}?t=${Date.now()}`; p.style.display='block'; p.play(); document.getElementById('pauseBtn').disabled=false; msg('开始播放','success'); startAutoSave(); await loadChapters(); }

function togglePlay(){ const p=document.getElementById('player'); if(p.paused){p.play(); this.innerText='⏸️ 暂停'} else {p.pause(); this.innerText='▶️ 播放'} }

async function batch(){ if(!bookId) return; msg('批量合成开始'); const q=new URLSearchParams({engine,voice}); const r=await fetch(`/batch/synthesize/${bookId}?`+q,{method:'POST'}); const x=await r.json(); if(!r.ok){ msg(x.detail||'失败','error'); return;} document.getElementById('batchPanel').style.display='block'; pollBatch(); }

let pollTimer=null; async function pollBatch(){ clearInterval(pollTimer); const state=await (await fetch('/batch/status')).json(); document.getElementById('batchState').innerText=`等待: ${state.pending||0} | 处理中: ${state.processing||0} | 完成: ${state.completed||0} | 失败: ${state.failed||0}`; document.getElementById('batchPanel').style.display='block'; pollTimer=setInterval(async()=>{const s=await (await fetch('/batch/status')).json(); document.getElementById('batchState').innerText=`等待: ${s.pending||0} | 处理中: ${s.processing||0} | 完成: ${s.completed||0} | 失败: ${s.failed||0}`; if((s.pending||0)+(s.processing||0)===0){ clearInterval(pollTimer); msg('批量完成','success'); loadChapters(); }},2000); }

async function saveProgress(){ const p=document.getElementById('player'); if(!bookId||!chapterId||!p.src) return; const q=new URLSearchParams({book_id:bookId,chapter_id:chapterId,position:p.currentTime}); await fetch('/progress/save?'+q,{method:'POST'}); }

function startAutoSave(){ const p=document.getElementById('player'); p.onpause=saveProgress; p.onended=saveProgress; setInterval(saveProgress,5000); }

async function loadProgress(){ const r=await fetch(`/progress/${bookId}`); if(!r.ok) return; const x=await r.json(); if(x && x.chapterId){ const box=document.getElementById('bookmarkBox'); const mm=Math.floor(x.position/60), ss=Math.floor(x.position%60).toString().padStart(2,'0'); box.innerText=`📍 上次播放：${x.chapterTitle} - ${mm}:${ss}`; box.style.display='block'; }
}

async function loadBooks(){ const r=await fetch('/books'); const list=await r.json(); if(Array.isArray(list) && list.length){ const shelf=document.getElementById('bookshelf'); shelf.style.display='block'; const box=document.getElementById('bookList'); box.innerHTML=list.map(b=>`<div class='card'><div style='font-weight:700'>${b.title}</div><div class='small'>章节: ${b.chapterCount} | 已缓存: ${b.cachedCount}</div><div style='margin-top:6px;display:flex;gap:6px'><button class='btn btn-primary' onclick='openBook(${b.id},${JSON.stringify(b.title)})'>打开</button><button class='btn btn-info' onclick='exportBook(${b.id})'>导出</button><button class='btn btn-danger' onclick='delBook(${b.id})'>删除</button></div></div>`).join(''); }
}

async function openBook(id,title){ bookId=id; document.getElementById('bookTitle').innerText=title; document.getElementById('bookInfo').style.display='block'; document.getElementById('engineBox').style.display='block'; document.getElementById('mainUI').style.display='grid'; await loadChapters(); await loadProgress(); }

async function delBook(id){ if(!confirm('确定删除该书及其音频缓存？')) return; const r=await fetch('/books/'+id,{method:'DELETE'}); if(r.ok){ msg('已删除','success'); await loadBooks(); } else { msg('删除失败','error'); } }

async function exportBook(id){ const bid=id||bookId; if(!bid){ msg('请先选择或导入一本书','error'); return;} msg('准备导出...'); const a=document.createElement('a'); a.href=`/export/${bid}?format=mp3`; a.download='audiobook.zip'; document.body.appendChild(a); a.click(); a.remove(); }
</script>
</body>
</html>
"""

    def run(self, host: str = "127.0.0.1", port: int = 8080):
        uvicorn.run(self.app, host=host, port=port, log_level="error", access_log=False)


def main():
    app = TTSApp()
    app.run()


if __name__ == "__main__":
    main()

