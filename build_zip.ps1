# 打包分发 ZIP（Windows PowerShell）
# 用法：右键用 PowerShell 运行 或在 PowerShell 执行：
#   ./build_zip.ps1 -Name "TTSReader_Windows_x64"
# 结果：dist/<Name>_YYYYMMDD.zip

param(
  [string]$Name = "TTSReader_Windows_x64"
)

$ErrorActionPreference = "Stop"

# 生成目标文件名
$today = Get-Date -Format yyyyMMdd
$zipPath = Join-Path "dist" ($Name + "_" + $today + ".zip")

# 收集要打包的文件到临时目录
$baseTemp = [System.IO.Path]::GetTempPath()
if (-not $baseTemp -or -not (Test-Path $baseTemp)) { $baseTemp = $PSScriptRoot }
$staging = Join-Path $baseTemp ("TTSReader_Zip_" + [Guid]::NewGuid().ToString("N"))
New-Item -ItemType Directory -Force -Path $staging | Out-Null

$copied = 0

$exe = "dist/TTSReader.exe"
if (Test-Path $exe) { Copy-Item $exe $staging; $copied++ }

$quickStart = "dist/快速开始-三步说明.txt"
if (Test-Path $quickStart) { Copy-Item $quickStart $staging; $copied++ }

$readme = "README.md"
if (Test-Path $readme) { Copy-Item $readme $staging; $copied++ }

$license = "LICENSE"
if (Test-Path $license) { Copy-Item $license $staging; $copied++ }

if ($copied -eq 0) {
  Remove-Item $staging -Recurse -Force -ErrorAction SilentlyContinue
  Write-Error "没有可打包的文件（未找到 dist/TTSReader.exe 等）"
  exit 1
}

if (Test-Path $zipPath) {
  Remove-Item $zipPath -Force
}

# 压缩临时目录下的内容（不包含临时目录本身）
Compress-Archive -Path (Join-Path $staging '*') -DestinationPath $zipPath -Force

# 清理临时目录（重试以避免文件占用）
$maxTries = 5
for ($i=0; $i -lt $maxTries; $i++) {
  try { Remove-Item $staging -Recurse -Force -ErrorAction Stop; break } catch { Start-Sleep -Milliseconds 300 }
}

Write-Host ("Created: " + $zipPath)

