# -*- coding: utf-8 -*-
"""/preprocess 路由：文本预处理
- 依据 bookId 读取TXT，执行：章节分割（正则优先→固定长度降级）、标点与空白规范化、特殊字符处理
- 生成章节索引入库 chapters，并返回章节列表
"""
from __future__ import annotations
import re
from pathlib import Path
from typing import List, Optional

from fastapi import APIRouter, HTTPException, Query
from pydantic import BaseModel
from sqlalchemy.orm import Session

from ..db.session import SessionLocal
from ..db.models import Book, Chapter
from ..utils.text_detect import detect_encoding_and_newlines

router = APIRouter()


class ChapterOut(BaseModel):
    id: int
    idx: int
    title: Optional[str]
    start_offset: Optional[int]
    end_offset: Optional[int]
    est_duration_sec: Optional[int]


class PreprocessResponse(BaseModel):
    bookId: int
    chapters: List[ChapterOut]


def get_db() -> Session:
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


def _normalize_text(s: str) -> str:
    """标点与空白粗略规范化（MVP）。
    - 连续空白折叠为单空格
    - 统一一些常见全角标点到半角
    - 清理非常规不可见字符
    """
    # 常见全角到半角简单映射（可扩展）
    trans = str.maketrans({
        '，': ',', '。': '.', '；': ';', '：': ':', '！': '!', '？': '?',
        '（': '(', '）': ')', '【': '[', '】': ']', '“': '"', '”': '"', '‘': "'", '’': "'",
    })
    s = s.translate(trans)
    # 折叠空白
    s = re.sub(r"\s+", " ", s)
    # 简单清理不可见控制字符（保留换行）
    s = re.sub(r"[\x00-\x08\x0B\x0C\x0E-\x1F]", "", s)
    return s


def _split_chapters(text: str, chapter_regex: Optional[str]) -> List[tuple]:
    """分章：返回[(title, start_idx, end_idx)]。
    - 正则优先：匹配章节标题位置
    - 失败时降级：固定长度块（例如每 ~8k 字，粗略）
    """
    spans: List[tuple] = []
    if chapter_regex:
        try:
            pattern = re.compile(chapter_regex, flags=re.IGNORECASE)
            matches = list(pattern.finditer(text))
            if matches:
                for i, m in enumerate(matches):
                    start = m.start()
                    end = matches[i + 1].start() if i + 1 < len(matches) else len(text)
                    title = m.group().strip()[:128]
                    spans.append((title, start, end))
        except re.error:
            # 正则非法则退化到固定长度
            pass
    if not spans:
        # 固定长度切块（约8000字符）
        chunk = 8000
        i = 0
        while i < len(text):
            start = i
            end = min(i + chunk, len(text))
            title = f"第{len(spans)+1}章"
            spans.append((title, start, end))
            i = end
    return spans


def _estimate_duration(chars: int, wpm: int = 300) -> int:
    """估算时长（秒）：粗略以每分钟300字。"""
    minutes = max(chars / max(wpm, 1), 0.1)
    return int(minutes * 60)


@router.post("/preprocess", response_model=PreprocessResponse, tags=["preprocess"])
async def preprocess(
    bookId: int = Query(..., description="书籍ID"),
    chapterRegex: Optional[str] = Query(None, description="章节正则，如^(第.+?章|Chapter\s+\d+)"),
    fallbackChunkSize: Optional[int] = Query(None, description="降级固定长度切片大小，默认8000"),
):
    # 读取书籍信息
    from fastapi import Depends
    db_gen = get_db()
    db = next(db_gen)
    try:
        book = db.query(Book).filter(Book.id == bookId).first()
        if not book:
            raise HTTPException(status_code=404, detail="书籍不存在")
        p = Path(book.file_path)
        if not p.exists():
            raise HTTPException(status_code=400, detail="文件路径无效")
        info = detect_encoding_and_newlines(p)
        encoding = book.encoding or info["encoding"]
        raw = p.read_text(encoding=encoding, errors="ignore")
        # 规范化文本（不回写磁盘，仅用于构建索引）
        norm = _normalize_text(raw)
        # 分章
        spans = _split_chapters(norm, chapterRegex)
        if not spans:
            raise HTTPException(status_code=400, detail="无法分章")
        # 入库前清理旧章节
        db.query(Chapter).filter(Chapter.book_id == bookId).delete()
        db.commit()
        # 写入新章节索引
        out: List[ChapterOut] = []
        for idx, (title, start, end) in enumerate(spans, start=1):
            est = _estimate_duration(end - start)
            ch = Chapter(
                book_id=bookId,
                idx=idx,
                title=title,
                start_offset=start,
                end_offset=end,
                est_duration_sec=est,
            )
            db.add(ch)
            db.flush()  # 获取ID
            out.append(
                ChapterOut(
                    id=ch.id,
                    idx=idx,
                    title=title,
                    start_offset=start,
                    end_offset=end,
                    est_duration_sec=est,
                )
            )
        db.commit()
        return PreprocessResponse(bookId=bookId, chapters=out)
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"预处理失败: {e}")
    finally:
        try:
            next(db_gen)
        except StopIteration:
            pass

