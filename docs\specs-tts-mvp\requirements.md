# 小说TXT转语音听书系统（MVP）需求说明（2025-08-08 星期五）

## Introduction
- Goal: Provide an offline-first TXT-to-speech listening app on Windows, supporting TXT import, preprocessing (chapter split/punctuation normalization/special-char handling), local-first TTS, per-chapter MP3 cache, a minimal player (play/pause/seek/jump), progress saving and bookmarks.
- Tech Baseline: Electron + React + TypeScript (frontend); FastAPI on Python (backend); SQLite (storage); TTS local-first pyttsx3 (SAPI5) with edge-tts as optional backup; MP3 cache per chapter; Windows 10/11 first.
- Note: Requirements are written with User Story (As a … I want … so that …) and EARS acceptance criteria (When/If/While … the system shall …).

## Requirements
### 1. TXT Import and Parsing
- User Story: As a reader, I want to import local TXT novels, so that I can start listening quickly.
- 用户故事：作为读者，我希望导入本地TXT小说，这样可以立即开始听书。
- Acceptance Criteria (EARS):
  1. When user selects a TXT file, the system shall auto-detect encoding (UTF-8/UTF-8-BOM/GBK) and allow manual override on failure.
  2. If line endings are mixed (\r\n/\n), the system shall normalize to an internal standard.
  3. While file size ≥ 50MB, the system shall use chunked/streaming read to avoid memory spikes.
  4. If import fails (invalid path/permission/encoding), the system shall show a readable error and minimal local log.

### 2. Text Preprocessing (Chapters/Punctuation/Special Chars)
- User Story: As a reader, I want automatic chapter splitting and cleanup, so that listening is natural and navigation is easy.
- 用户故事：作为读者，我希望自动分章与清理文本，这样朗读更自然、跳转更方便。
- Acceptance Criteria (EARS):
  1. When typical chapter titles are detected (e.g., “第1章”/“第一百二十章”/“Chapter 1”), the system shall split by configurable regex; otherwise provide fallback by fixed length/paragraphs.
  2. When full/half-width mixing or excessive punctuation/whitespace occurs, the system shall normalize without changing meaning.
  3. If special/invisible chars exist, the system shall sanitize or replace with safe placeholders.
  4. When splitting finishes, the system shall produce a chapter index (title, offsets, ETA duration).

### 3. TTS Engine Integration
- User Story: As a listener, I want high-quality TTS with voice/language options, so that I can customize the experience.
- 用户故事：作为读者，我希望高质量合成并可选音色与语种，以便自定义体验。
- Acceptance Criteria (EARS):
  1. If offline, the system shall use local SAPI5 (pyttsx3) to synthesize.
  2. When cloud backup (edge-tts) is enabled and network is available, the system shall allow switching cloud voices/languages.
  3. If synthesis fails (driver/device), the system shall retry limited times and provide fallback engine/strategy.
  4. When user changes rate/pitch/pauses, the system shall apply to subsequent synthesis jobs.

### 4. Audio Quality Controls (Rate/Pitch/Pauses)
- User Story: As a listener, I want to adjust speech parameters, so that the audio feels comfortable.
- 用户故事：作为读者，我希望可调语速/音调/停顿，使听感舒适。
- Acceptance Criteria (EARS):
  1. When rate changes (e.g., 0.5x–2.0x), the system shall apply at playback layer (real-time) and synthesis layer (future jobs).
  2. When encountering sentence/paragraph boundaries, the system shall insert natural pauses with a global adjustable baseline.
  3. If continuous punctuation/ellipsis appears, the system shall avoid over-pausing.
  4. When exporting MP3, the system shall use mono, fixed sample rate (e.g., 22050/24000Hz) and stable bitrate (e.g., 128kbps).

### 5. Playback Controls (Play/Pause/Seek/Chapter Jump)
- User Story: As a listener, I want responsive controls and chapter jumps, so that I can navigate efficiently.
- 用户故事：作为读者，我希望便捷控制并快速章节跳转。
- Acceptance Criteria (EARS):
  1. When play/pause is clicked, the system shall respond within 100ms.
  2. When seeking forward/backward (default 5–15s configurable), the system shall position accurately within the chapter.
  3. When a chapter is selected, the system shall start playback within 500ms if cached.
  4. If audio is not cached yet, the system shall show “generating/loading” and auto-start when ready.

### 6. Progress and Bookmarks
- User Story: As a listener, I want auto progress and bookmarks, so that I can resume and revisit easily.
- 用户故事：作为读者，我希望自动保存进度并可打书签，便于恢复与回看。
- Acceptance Criteria (EARS):
  1. While playing (every N seconds or key events), the system shall persist book/chapter/timestamp to SQLite.
  2. When app restarts, the system shall offer “resume last position” and locate within ±2s tolerance.
  3. When a bookmark is added, the system shall store name/notes/timestamp and allow jump/delete.

### 7. Minimal Player UI
- User Story: As a user, I want a minimal player UI, so that I can focus on listening.
- 用户故事：作为用户，我希望简洁播放器界面以专注听书。
- Acceptance Criteria (EARS):
  1. When app starts, the system shall show minimal controls: import, play controls, progress bar, rate slider, chapter panel.
  2. While window scales or on HiDPI, the system shall keep clarity and touch targets.
  3. When theme changes (light/dark), the system shall keep consistent contrast/readability.
  4. The system shall provide basic shortcuts (Space, Ctrl+→/←).

### 8. Caching and Performance (Per-Chapter MP3)
- User Story: As a user, I want chapter-level MP3 cache, so that replay is instant and synthesis is reduced.
- 用户故事：作为用户，我希望章节级MP3缓存以便快速二次播放、减少重复合成。
- Acceptance Criteria (EARS):
  1. When first playing/pre-generating a chapter, the system shall persist MP3 and index mapping (chapter→file path).
  2. When replaying cached chapters, the system shall start within ≤300ms on normal disks.
  3. If disk space is low, the system shall suggest cleanup (LRU/by size threshold).
  4. While handling ≥50MB novels, the system shall batch/defer synthesis to avoid long blocking.

### 9. Compatibility and Portability
- User Story: As a user, I want stable Win10/11 support and Chinese paths, so that common scenarios work.
- 用户故事：作为用户，我希望在Win10/11稳定运行并兼容中文路径。
- Acceptance Criteria (EARS):
  1. When TXT/book names contain Chinese/spaces/special chars, the system shall handle import/cache/DB correctly.
  2. On Win10/11 Home/Pro, the system shall work without extra drivers (SAPI5 baseline).
  3. If offline, the system shall remain usable (local TTS/playback).

### 10. Error Handling and Observability
- User Story: As a user, I want clear errors and retry, so that issues don’t block listening.
- 用户故事：作为用户，我希望出错时有清晰提示与可重试，不阻断体验。
- Acceptance Criteria (EARS):
  1. When import/preprocess/TTS/playback errors occur, the system shall show friendly messages and minimal logs.
  2. If failures exceed a threshold, the system shall pause batches and suggest checks (e.g., device/TTS availability).
  3. By default, the system shall avoid sensitive logs; only diagnostic mode increases verbosity.

### 11. Non-Functional (Performance/Resources/Startup)
- User Story: As a user, I want quick startup and controlled resource usage, so that the app feels smooth.
- 用户故事：作为用户，我希望快速启动且资源可控，保证流畅。
- Acceptance Criteria (EARS):
  1. When cold start on a mid PC (4C/8G/SSD), the system shall start within ≤3s (excluding first install).
  2. While background synthesis runs, the system shall limit CPU/concurrency to keep UI playback smooth.
  3. The system shall keep per-chapter MP3 size within configurable profiles (quality/size balance by default).

### 12. Data and Storage
- User Story: As a user, I want structured data, so that recovery/migration is easy.
- 用户故事：作为用户，我希望数据有序可追溯，便于恢复与迁移。
- Acceptance Criteria (EARS):
  1. The system shall manage SQLite tables: books, chapters (title/offset/cache), progress (book/chapter/ts), bookmarks (name/notes/ts).
  2. The system shall import/export settings and progress via JSON.

### 13. Security and Privacy
- User Story: As a privacy-conscious user, I want no uploads by default, so that my data stays local.
- 用户故事：作为注重隐私的用户，我希望默认不上传，数据留在本地。
- Acceptance Criteria (EARS):
  1. By default, the system shall not upload text/audio; only on explicit enablement of cloud TTS it shall send minimal data.
  2. The system shall store config/DB in a visible directory and allow user cleanup.

### 14. Usability and Accessibility
- User Story: As a user, I want basic accessibility, so that controls are usable.
- 用户故事：作为用户，我希望具备基础无障碍可用性。
- Acceptance Criteria (EARS):
  1. The system shall provide sufficient contrast and touch targets; focus states shall be clear.
  2. The system shall provide keyboard shortcuts and status hints for common actions.

## Open Questions
- Should the chapter-detection priority and visual debugging UI be included in MVP?
- Should non-TXT (epub/pdf) import be postponed?
- Should MP3 defaults (sample rate/bitrate) and profiles be exposed in Settings?
- Should cloud sync for progress/bookmarks be deferred to future releases?

