# -*- coding: utf-8 -*-
"""
批量合成任务管理
- 支持批量预合成章节
- 异步任务队列
- 进度跟踪和状态管理
"""
from __future__ import annotations
import asyncio
import threading
from typing import List, Optional, Dict, Any
from pathlib import Path
from datetime import datetime
import logging
from enum import Enum
from dataclasses import dataclass, field
import queue

logger = logging.getLogger(__name__)


class TaskStatus(Enum):
    """任务状态枚举"""
    PENDING = "pending"      # 等待中
    PROCESSING = "processing" # 处理中
    COMPLETED = "completed"   # 已完成
    FAILED = "failed"        # 失败
    CANCELLED = "cancelled"   # 已取消


@dataclass
class SynthesisTask:
    """合成任务数据类"""
    task_id: str
    book_id: int
    chapter_id: int
    chapter_idx: int
    text: str
    engine: str = "local"  # local or cloud
    voice: Optional[str] = None
    rate: Optional[int] = None
    status: TaskStatus = TaskStatus.PENDING
    progress: float = 0.0
    error_message: Optional[str] = None
    created_at: datetime = field(default_factory=datetime.now)
    completed_at: Optional[datetime] = None
    output_path: Optional[Path] = None
    retry_count: int = 0
    max_retries: int = 3


class BatchSynthesizer:
    """批量合成器"""
    
    def __init__(self, max_workers: int = 2):
        """
        Args:
            max_workers: 最大并发工作线程数
        """
        self.max_workers = max_workers
        self.task_queue: queue.Queue = queue.Queue()
        self.active_tasks: Dict[str, SynthesisTask] = {}
        self.completed_tasks: Dict[str, SynthesisTask] = {}
        self.workers: List[threading.Thread] = []
        self.running = False
        self.lock = threading.Lock()
        
        # 导入TTS服务
        self._import_tts_services()
    
    def _import_tts_services(self):
        """延迟导入TTS服务"""
        try:
            from ..services.tts_local import synthesize_wav
            self.synthesize_wav = synthesize_wav
            self.has_local_tts = True
        except ImportError:
            self.has_local_tts = False
            logger.warning("本地TTS服务不可用")
        
        try:
            from ..services.tts_cloud import synthesize_mp3, HAS_EDGE_TTS
            self.synthesize_mp3 = synthesize_mp3
            self.has_cloud_tts = HAS_EDGE_TTS
        except ImportError:
            self.has_cloud_tts = False
            logger.warning("云端TTS服务不可用")
        
        try:
            from ..services.audio_encode import wav_to_mp3
            self.wav_to_mp3 = wav_to_mp3
            self.has_audio_converter = True
        except ImportError:
            self.has_audio_converter = False
            logger.warning("音频转换服务不可用")
    
    def start(self):
        """启动批量合成服务"""
        if self.running:
            return
        
        self.running = True
        for i in range(self.max_workers):
            worker = threading.Thread(target=self._worker, name=f"SynthWorker-{i+1}")
            worker.daemon = True
            worker.start()
            self.workers.append(worker)
        
        logger.info(f"批量合成服务已启动，工作线程数: {self.max_workers}")
    
    def stop(self):
        """停止批量合成服务"""
        self.running = False
        
        # 清空队列
        while not self.task_queue.empty():
            try:
                self.task_queue.get_nowait()
            except queue.Empty:
                break
        
        # 等待工作线程结束
        for worker in self.workers:
            worker.join(timeout=5)
        
        self.workers.clear()
        logger.info("批量合成服务已停止")
    
    def _worker(self):
        """工作线程主循环"""
        while self.running:
            try:
                # 获取任务（阻塞等待，超时1秒）
                task = self.task_queue.get(timeout=1)
                
                # 更新任务状态
                with self.lock:
                    task.status = TaskStatus.PROCESSING
                    self.active_tasks[task.task_id] = task
                
                # 执行合成
                self._process_task(task)
                
                # 更新完成状态
                with self.lock:
                    if task.task_id in self.active_tasks:
                        del self.active_tasks[task.task_id]
                    self.completed_tasks[task.task_id] = task
                
            except queue.Empty:
                continue
            except Exception as e:
                logger.error(f"工作线程异常: {e}")
    
    def _process_task(self, task: SynthesisTask):
        """处理单个合成任务"""
        try:
            # 确定输出路径
            from ..core.config import settings
            if task.engine == "cloud":
                output_path = settings.get_cache_path(task.book_id, task.chapter_idx, "mp3")
            else:
                output_path = settings.get_cache_path(task.book_id, task.chapter_idx, "wav")
            
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 执行合成
            if task.engine == "cloud" and self.has_cloud_tts:
                # 使用云端TTS
                self.synthesize_mp3(
                    task.text, 
                    output_path,
                    voice=task.voice or "zh-CN-XiaoxiaoNeural",
                    rate=f"+{task.rate-100}%" if task.rate else "+0%"
                )
            elif self.has_local_tts:
                # 使用本地TTS
                self.synthesize_wav(task.text, output_path, rate=task.rate)
                
                # 尝试转换为MP3
                if self.has_audio_converter and task.engine != "local":
                    mp3_path = output_path.with_suffix('.mp3')
                    if self.wav_to_mp3(output_path, mp3_path):
                        output_path.unlink()  # 删除WAV文件
                        output_path = mp3_path
            else:
                raise Exception("没有可用的TTS引擎")
            
            # 更新任务状态
            task.status = TaskStatus.COMPLETED
            task.progress = 100.0
            task.completed_at = datetime.now()
            task.output_path = output_path
            
            logger.info(f"任务 {task.task_id} 合成完成: {output_path}")
            
        except Exception as e:
            # 处理失败
            task.retry_count += 1
            if task.retry_count < task.max_retries:
                # 重新加入队列
                task.status = TaskStatus.PENDING
                self.task_queue.put(task)
                logger.warning(f"任务 {task.task_id} 失败，重试 {task.retry_count}/{task.max_retries}")
            else:
                # 标记为失败
                task.status = TaskStatus.FAILED
                task.error_message = str(e)
                task.completed_at = datetime.now()
                logger.error(f"任务 {task.task_id} 最终失败: {e}")
    
    def add_task(self, task: SynthesisTask) -> str:
        """添加合成任务
        
        Args:
            task: 合成任务
        
        Returns:
            任务ID
        """
        self.task_queue.put(task)
        logger.info(f"添加任务 {task.task_id} 到队列")
        return task.task_id
    
    def add_batch_tasks(self, tasks: List[SynthesisTask]) -> List[str]:
        """批量添加任务
        
        Args:
            tasks: 任务列表
        
        Returns:
            任务ID列表
        """
        task_ids = []
        for task in tasks:
            task_id = self.add_task(task)
            task_ids.append(task_id)
        return task_ids
    
    def get_task_status(self, task_id: str) -> Optional[SynthesisTask]:
        """获取任务状态
        
        Args:
            task_id: 任务ID
        
        Returns:
            任务对象，如果不存在则返回None
        """
        with self.lock:
            # 检查活动任务
            if task_id in self.active_tasks:
                return self.active_tasks[task_id]
            # 检查完成任务
            if task_id in self.completed_tasks:
                return self.completed_tasks[task_id]
        return None
    
    def get_queue_status(self) -> Dict[str, Any]:
        """获取队列状态
        
        Returns:
            包含队列统计信息的字典
        """
        with self.lock:
            return {
                "pending": self.task_queue.qsize(),
                "processing": len(self.active_tasks),
                "completed": len(self.completed_tasks),
                "workers": self.max_workers,
                "running": self.running
            }
    
    def cancel_task(self, task_id: str) -> bool:
        """取消任务
        
        Args:
            task_id: 任务ID
        
        Returns:
            是否成功取消
        """
        # 注意：队列中的任务无法直接取消，只能标记
        with self.lock:
            if task_id in self.active_tasks:
                self.active_tasks[task_id].status = TaskStatus.CANCELLED
                return True
        return False
    
    def clear_completed_tasks(self):
        """清理已完成的任务记录"""
        with self.lock:
            self.completed_tasks.clear()
        logger.info("已清理完成任务记录")


# 全局批量合成器实例
batch_synthesizer = BatchSynthesizer(max_workers=2)
