# -*- coding: utf-8 -*-
"""
主应用入口（FastAPI）
- 提供健康检查/根路径说明
- 组装路由、数据库初始化（MVP阶段直接建表）
- 中文注释，便于后续维护
"""
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

from .api.routes import api_router
from .db.session import engine
from .db.models import Base

app = FastAPI(title="TXT→TTS 听书系统（MVP）", version="0.1.0")

# CORS：允许前端Vite开发端口访问
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:5173",
        "http://127.0.0.1:5173",
    ],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.on_event("startup")
async def on_startup() -> None:
    """启动时初始化数据库（MVP：直接创建表）。
    注意：正式环境建议使用迁移工具（如Alembic），此处为快速可用的骨架实现。
    """
    Base.metadata.create_all(bind=engine)


@app.get("/", tags=["root"])
async def root():
    """根路径：简单返回项目说明。"""
    return {"name": "TXT→TTS 听书系统（MVP）", "docs": "/docs", "health": "/health"}


# 组装API路由
app.include_router(api_router)

