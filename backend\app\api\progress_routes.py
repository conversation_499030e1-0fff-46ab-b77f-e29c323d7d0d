# -*- coding: utf-8 -*-
"""进度与书签 API
- /progress/save 保存某本书的最后播放位置
- /progress/last 获取某本书的最后播放位置
- /bookmark/add 增加书签
- /bookmark/list 列出书签
- /bookmark/delete 删除书签
"""
from __future__ import annotations
from typing import Optional, List
from datetime import datetime

from fastapi import APIRouter, HTTPException, Query
from pydantic import BaseModel
from sqlalchemy.orm import Session

from ..db.session import SessionLocal
from ..db.models import Book, Chapter, Progress, Bookmark

router = APIRouter()


class ProgressOut(BaseModel):
    bookId: int
    chapterId: Optional[int]
    positionMs: int
    updatedAt: datetime


class ProgressLastResponse(BaseModel):
    found: bool
    data: Optional[ProgressOut] = None


class BookmarkOut(BaseModel):
    id: int
    bookId: int
    chapterId: Optional[int]
    positionMs: int
    name: Optional[str]
    note: Optional[str]
    createdAt: datetime


def get_db() -> Session:
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


@router.post("/progress/save", response_model=ProgressOut, tags=["progress"])
async def save_progress(
    bookId: int = Query(..., description="书籍ID"),
    chapterId: Optional[int] = Query(None, description="章节ID，可为空"),
    positionMs: int = Query(..., description="播放位置（毫秒）"),
):
    db_gen = get_db()
    db = next(db_gen)
    try:
        book = db.query(Book).filter(Book.id == bookId).first()
        if not book:
            raise HTTPException(status_code=404, detail="书籍不存在")
        if chapterId is not None:
            ch = db.query(Chapter).filter(Chapter.id == chapterId, Chapter.book_id == bookId).first()
            if not ch:
                raise HTTPException(status_code=404, detail="章节不存在")
        # 单本书一条记录：存在则更新，不存在则创建
        rec = db.query(Progress).filter(Progress.book_id == bookId).first()
        now = datetime.utcnow()
        if rec:
            rec.chapter_id = chapterId
            rec.position_ms = max(int(positionMs), 0)
            rec.updated_at = now
        else:
            rec = Progress(book_id=bookId, chapter_id=chapterId, position_ms=max(int(positionMs), 0), updated_at=now)
            db.add(rec)
        db.commit()
        db.refresh(rec)
        return ProgressOut(bookId=bookId, chapterId=rec.chapter_id, positionMs=rec.position_ms, updatedAt=rec.updated_at)
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"保存进度失败: {e}")
    finally:
        try:
            next(db_gen)
        except StopIteration:
            pass


@router.get("/progress/last", response_model=ProgressLastResponse, tags=["progress"])
async def last_progress(
    bookId: int = Query(..., description="书籍ID"),
):
    db_gen = get_db()
    db = next(db_gen)
    try:
        book = db.query(Book).filter(Book.id == bookId).first()
        if not book:
            raise HTTPException(status_code=404, detail="书籍不存在")
        rec = db.query(Progress).filter(Progress.book_id == bookId).first()
        if not rec:
            return ProgressLastResponse(found=False)
        return ProgressLastResponse(
            found=True,
            data=ProgressOut(bookId=bookId, chapterId=rec.chapter_id, positionMs=rec.position_ms, updatedAt=rec.updated_at),
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"查询进度失败: {e}")
    finally:
        try:
            next(db_gen)
        except StopIteration:
            pass


@router.post("/bookmark/add", response_model=BookmarkOut, tags=["bookmark"])
async def add_bookmark(
    bookId: int = Query(..., description="书籍ID"),
    chapterId: Optional[int] = Query(None, description="章节ID，可为空"),
    positionMs: int = Query(..., description="位置（毫秒）"),
    name: Optional[str] = Query(None, description="书签名称"),
    note: Optional[str] = Query(None, description="备注"),
):
    db_gen = get_db()
    db = next(db_gen)
    try:
        book = db.query(Book).filter(Book.id == bookId).first()
        if not book:
            raise HTTPException(status_code=404, detail="书籍不存在")
        if chapterId is not None:
            ch = db.query(Chapter).filter(Chapter.id == chapterId, Chapter.book_id == bookId).first()
            if not ch:
                raise HTTPException(status_code=404, detail="章节不存在")
        rec = Bookmark(
            book_id=bookId,
            chapter_id=chapterId,
            position_ms=max(int(positionMs), 0),
            name=name,
            note=note,
        )
        db.add(rec)
        db.commit()
        db.refresh(rec)
        return BookmarkOut(
            id=rec.id,
            bookId=rec.book_id,
            chapterId=rec.chapter_id,
            positionMs=rec.position_ms,
            name=rec.name,
            note=rec.note,
            createdAt=rec.created_at,
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"新增书签失败: {e}")
    finally:
        try:
            next(db_gen)
        except StopIteration:
            pass


class BookmarkListResponse(BaseModel):
    bookId: int
    items: List[BookmarkOut]


@router.get("/bookmark/list", response_model=BookmarkListResponse, tags=["bookmark"])
async def list_bookmarks(
    bookId: int = Query(..., description="书籍ID"),
):
    db_gen = get_db()
    db = next(db_gen)
    try:
        book = db.query(Book).filter(Book.id == bookId).first()
        if not book:
            raise HTTPException(status_code=404, detail="书籍不存在")
        q = db.query(Bookmark).filter(Bookmark.book_id == bookId).order_by(Bookmark.created_at.desc())
        items = [
            BookmarkOut(
                id=r.id,
                bookId=r.book_id,
                chapterId=r.chapter_id,
                positionMs=r.position_ms,
                name=r.name,
                note=r.note,
                createdAt=r.created_at,
            )
            for r in q.all()
        ]
        return BookmarkListResponse(bookId=bookId, items=items)
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取书签列表失败: {e}")
    finally:
        try:
            next(db_gen)
        except StopIteration:
            pass


@router.delete("/bookmark/delete", tags=["bookmark"])
async def delete_bookmark(
    bookmarkId: int = Query(..., description="书签ID"),
):
    db_gen = get_db()
    db = next(db_gen)
    try:
        rec = db.query(Bookmark).filter(Bookmark.id == bookmarkId).first()
        if not rec:
            raise HTTPException(status_code=404, detail="书签不存在")
        db.delete(rec)
        db.commit()
        return {"ok": True}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除书签失败: {e}")
    finally:
        try:
            next(db_gen)
        except StopIteration:
            pass

