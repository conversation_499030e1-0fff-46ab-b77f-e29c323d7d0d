# TXT小说转语音听书系统（Electron+React前端 + FastAPI后端 + 混合TTS） - Requirements Document

本项目旨在构建一个支持TXT小说文档导入、文本预处理（章节分割、标点优化、特殊字符处理）、TTS转换（本地优先，云端可选）、语音质量控制（语速/音调/停顿）、播放控制（播放/暂停/快进/倒退/章节跳转）、进度与书签保存、简洁UI等功能的一体化桌面听书系统。技术架构采用Electron+React+TypeScript作为桌面前端，Python FastAPI作为后端服务层，SQLite用于本地数据持久化。音频采用分章节MP3落地缓存，优先Windows平台，支持多语种多音色可切换。

## 一、核心功能（Core Features）

1. TXT文档导入与解析：支持大文件（≥50MB）按流式读取；自动识别UTF-8/GBK等编码；异常编码回退与提示。
2. 文本预处理：
   - 章节分割：支持正则规则（第X章/Chapter/卷/番外）和自定义规则；失败时启用基于长度与标点的启发式切分。
   - 标点优化：统一全半角，修复连续标点，句末补全句号/问号/感叹号；中文排版空格规范化。
   - 特殊字符处理：清理不可见字符、BOM、乱码；保留必要的换行与段落结构。
3. TTS引擎集成（混合模式）：
   - 本地：优先Windows系统TTS或edge-tts；
   - 云端：可配置Azure/Edge/Google/ElevenLabs；
   - 热切换：网络不可用自动降级至本地；支持代理设置与密钥管理。
4. 语音输出质量控制：
   - 参数：语速、音调、音量、停顿；
   - 合成：分章节批量合成为MP3（CBR 128kbps 默认，可调）；
   - 切片：可选按自然段切片并拼接，提升跳转/续播体验。
5. 播放控制：
   - 基础：播放/暂停/停止/上一段/下一段/快进/快退；
   - 跳转：按章节/段落/时间点跳转；
   - 播放列表：支持章节列表与当前播放高亮。
6. 进度与书签：
   - 自动保存听书进度（书名、章节、段内偏移、时间点）；
   - 多书签管理，书签命名与备注；
   - 跨会话恢复。
7. 用户界面：
   - Electron + React + TS：简洁播放器UI（控制区、进度条、章节侧栏、设置抽屉）；
   - 批量导入、合成状态、错误提示与重试；
   - 可选深色模式、快捷键（空格/左右箭头/上下箭头）。

## 二、用户故事（User Stories）

1. 作为重度小说听众，我希望能一键导入TXT并自动分章，这样可以按章节有序收听。
2. 作为细节控用户，我希望能调节语速/音调/停顿，使得不同书籍与场景获得最佳音色体验。
3. 作为通勤用户，我希望播放支持快进/倒退与章节跳转，以便快速定位内容。
4. 作为多设备用户，我希望系统能保存我的进度和书签，下次打开时自动恢复。
5. 作为省心用户，我希望在网络不佳时自动使用本地TTS，保证随时可听。
6. 作为多语读者，我希望可选多语种和多音色，以适应中英文和不同人物风格。

## 三、验收标准（Acceptance Criteria，EARS）

1) 当用户导入TXT文件时，系统应当自动识别编码并成功读取≥50MB文件，失败时给出可读错误与重试/更改编码选项。
2) 当文本预处理执行时，系统应当基于规则成功分章；若规则匹配失败，应当启用启发式切分并提示用户可调整。
3) 当用户选择本地/云端TTS时，系统应当在≤2秒内完成引擎可用性检测；当云端不可用时，应当自动降级到本地引擎并提示。
4) 当执行合成时，系统应当按章节生成MP3文件，默认比特率128kbps，文件命名含书名与章节序号；当磁盘空间不足时，应当停止合成并提示释放空间。
5) 当用户调整语速/音调/停顿时，系统应当在下一次合成与实时预听中体现变化；当参数越界时，应当限制在安全范围并提示。
6) 当播放时，系统应当支持播放/暂停/上一段/下一段/快进10s/快退10s；当跳转到章节时，应当在≤500ms内开始播放目标音频。
7) 当用户添加书签时，系统应当保存书签并允许重命名与删除；当应用重启时，应当恢复上次播放进度与书签列表。
8) 当进行批量导入与合成时，系统应当显示队列与进度百分比；当任何任务失败时，应当提供单任务重试与失败原因。
9) 当用户界面处于深色模式设置时，系统应当全局应用深色主题；当切换主题时，应当在≤300ms内完成无闪烁切换。

## 四、非功能性需求（Non-functional Requirements）

1. 性能：
   - 打开应用到可操作主界面≤2s（冷启动≤5s）；
   - 章节合成并写盘吞吐≥1x 实时（1小时文本≤1小时合成完成，具体依赖TTS引擎）。
2. 可靠性与容错：
   - 云TTS失败自动降级本地；
   - 合成与播放任务可恢复，异常中断后不损坏已生成文件。
3. 兼容性：
   - Windows 10/11；未来扩展到macOS需抽象音频层。
4. 安全与隐私：
   - API密钥加密存储（DPAPI/本地加密）；
   - 本地音频与进度数据仅存储在用户目录，可一键清理缓存。
5. 可维护性：
   - 前后端接口OpenAPI文档化；
   - 模块边界清晰（导入/预处理/TTS/存储/播放/UI）。
6. 可用性：
   - 键盘快捷键与无障碍（焦点可达、屏幕阅读器标签）。