# -*- coding: utf-8 -*-
"""云端TTS（edge-tts）封装
- 提供Microsoft Edge云端语音合成服务
- 支持多种语音和语速调节
- 输出：保存为MP3文件（edge-tts原生支持）
"""
from __future__ import annotations
import asyncio
from pathlib import Path
from typing import Optional, List
import logging

logger = logging.getLogger(__name__)

# 延迟导入，避免未安装时模块级导入失败
try:
    import edge_tts
    HAS_EDGE_TTS = True
except ImportError:
    HAS_EDGE_TTS = False
    logger.warning("edge-tts未安装，云端TTS功能不可用")


# 常用中文语音列表
CHINESE_VOICES = [
    "zh-CN-XiaoxiaoNeural",  # 女声，活泼
    "zh-CN-YunxiNeural",     # 男声，标准
    "zh-CN-YunyangNeural",   # 男声，新闻
    "zh-CN-XiaoyiNeural",    # 女声，温柔
    "zh-CN-YunjianNeural",   # 男声，体育解说
    "zh-CN-XiaoxuanNeural",  # 女声，成熟
]


async def synthesize_mp3_async(
    text: str, 
    mp3_path: Path, 
    voice: str = "zh-CN-XiaoxiaoNeural",
    rate: str = "+0%",
    volume: str = "+0%"
) -> None:
    """使用 edge-tts 异步合成到 MP3 文件。
    
    Args:
        text: 要合成的文本
        mp3_path: 输出MP3文件路径
        voice: 语音名称，默认使用晓晓
        rate: 语速调整，如 "-50%" 或 "+25%"
        volume: 音量调整，如 "-50%" 或 "+25%"
    
    Raises:
        ImportError: 如果edge-tts未安装
        Exception: 合成过程中的其他错误
    """
    if not HAS_EDGE_TTS:
        raise ImportError("edge-tts未安装，请运行: pip install edge-tts")
    
    mp3_path.parent.mkdir(parents=True, exist_ok=True)
    
    try:
        # 创建通信对象
        communicate = edge_tts.Communicate(text, voice, rate=rate, volume=volume)
        # 保存到文件
        await communicate.save(mp3_path.as_posix())
        logger.info(f"云端TTS合成成功: {mp3_path}")
    except Exception as e:
        logger.error(f"云端TTS合成失败: {e}")
        raise


def synthesize_mp3(text: str, mp3_path: Path, **kwargs) -> None:
    """同步接口包装器"""
    asyncio.run(synthesize_mp3_async(text, mp3_path, **kwargs))


async def list_voices_async(language: Optional[str] = "zh-CN") -> List[dict]:
    """获取可用语音列表
    
    Args:
        language: 语言代码，如 "zh-CN"，None 表示所有语言
    
    Returns:
        语音信息列表，包含 Name, ShortName, Gender 等字段
    """
    if not HAS_EDGE_TTS:
        return []
    
    try:
        voices = await edge_tts.list_voices()
        if language:
            voices = [v for v in voices if v["Locale"].startswith(language)]
        return voices
    except Exception as e:
        logger.error(f"获取语音列表失败: {e}")
        return []


def list_voices(language: Optional[str] = "zh-CN") -> List[dict]:
    """同步接口包装器"""
    return asyncio.run(list_voices_async(language))


def get_recommended_voices() -> List[dict]:
    """获取推荐的中文语音列表"""
    return [
        {"name": "晓晓", "value": "zh-CN-XiaoxiaoNeural", "gender": "女", "style": "活泼"},
        {"name": "云希", "value": "zh-CN-YunxiNeural", "gender": "男", "style": "标准"},
        {"name": "晓伊", "value": "zh-CN-XiaoyiNeural", "gender": "女", "style": "温柔"},
        {"name": "云扬", "value": "zh-CN-YunyangNeural", "gender": "男", "style": "新闻"},
        {"name": "晓萱", "value": "zh-CN-XiaoxuanNeural", "gender": "女", "style": "成熟"},
        {"name": "云健", "value": "zh-CN-YunjianNeural", "gender": "男", "style": "体育"},
    ]
