# TXT小说转语音听书系统（Electron+React前端 + FastAPI后端 + 混合TTS） - Design Document

## Overview（概览）

本系统采用桌面端优先策略：Electron + React + TypeScript 提供跨平台（首期以 Windows 10/11 为主）的现代化播放器界面；Python FastAPI 作为本地后端服务进程，负责TXT解析、文本预处理、TTS合成与音频缓存。引入“本地优先、云端可切换”的混合TTS策略，在网络不可用或配额受限时自动降级，保障可用性。音频以“分章节MP3落地缓存”为核心，结合段内切片索引，提升跳转/续播体验与容错能力。

关键设计目标：
- 稳定性：TTS供应商故障或网络问题不致中断收听（自动降级、本地回退）。
- 性能：流式读取与并发合成，单机≥1x实时合成吞吐（受限于TTS）。
- 易用性：简洁播放器、快捷键、章节侧栏、设置抽屉、进度与书签自动保存。
- 可维护性：清晰的模块边界与接口契约，OpenAPI生成前端SDK。

## Architecture（架构）

```mermaid
flowchart TB
  UI[Electron + React UI] -- IPC/HTTP --> API[FastAPI Service]
  API -- Parse/Preprocess --> TXT[TXT Importer]
  API -- TTS (Local/Cloud) --> TTS[(TTS Engines)]
  API -- Writes --> CACHE[(Audio Cache MP3)]
  API -- Persist --> DB[(SQLite)]
  UI -- Read/Stream --> CACHE
  UI -- CRUD --> DB
```

- Electron主进程：负责窗口管理、文件系统权限桥接、后端进程启动/心跳监督。
- React渲染进程：播放器界面、列表与设置，调用FastAPI（本机127.0.0.1）或IPC包装。
- FastAPI服务：
  - 导入/解析模块：编码探测、流式读取、章节规则与启发式切分。
  - 预处理模块：标点优化、特殊字符清理、排版规范化。
  - TTS编排器：本地引擎适配器、云引擎适配器、策略与降级、参数控制。
  - 合成与缓存：分章节/分段合成、MP3编码、文件命名规范、磁盘配额检查。
  - 进度与书签：CRUD API，SQLite持久化。

## Components and Interfaces（组件与接口）

前端（React）：
- Views：Library（书库/导入）、Player（播放器）、Chapters（章节侧栏）、Settings（设置）、Tasks（合成队列）。
- Hooks：usePlayer、useChapters、useSettings、useTasks、useBookmarks。
- Services：apiClient（OpenAPI生成）
- IPC桥接：electronBridge（启动/检测后端服务、选择文件、权限提示）。

后端（FastAPI）：
- /import
  - POST /import/txt {path|file} → bookId：导入TXT并建立章节索引。
- /preprocess
  - POST /preprocess/{bookId} {rules, options} → updated meta。
- /tts
  - POST /tts/chapters {bookId, chapterIds, voice, rate, pitch, pause, enginePreference} → taskId
  - GET /tts/tasks/{taskId} → {status, progress, errors}
- /audio
  - GET /audio/{bookId}/{chapterId}.mp3 → 流式播放
- /playback
  - POST /playback/progress {bookId, chapterId, offsetSec}
  - GET /playback/progress/{bookId}
- /bookmarks
  - POST/GET/PUT/DELETE /bookmarks/{bookId}
- /settings
  - GET/PUT /settings（TTS参数、云端密钥、缓存路径、主题）

接口契约：采用OpenAPI 3，前端通过openapi-typescript生成类型安全SDK。

TTS引擎适配：
- LocalEngineAdapter：Windows SAPI 或 edge-tts（Python库），支持语速(rate)、音调(pitch)、音量(volume)、停顿(pause)。
- CloudEngineAdapter：Azure/Edge/Google/ElevenLabs，统一接口合成，并支持超时/重试/限速与费用估算。
- EngineOrchestrator：选择策略（优先本地→云备援）、健康检查、失败回退。

## Data Models（数据模型）

数据库（SQLite）：
- books(id, title, author, file_path, charset, chapter_count, created_at)
- chapters(id, book_id, index, title, start_offset, end_offset, audio_path, duration_sec, hash)
- bookmarks(id, book_id, chapter_id, offset_sec, label, note, created_at)
- playback_state(book_id, chapter_id, offset_sec, updated_at)
- settings(id=1, theme, cache_dir, tts_preset, engine_pref, cloud_keys_encrypted)
- tasks(id, type, book_id, payload_json, status, progress, error, created_at, updated_at)

文件系统：
- cache/{bookId}/chapter-{index}.mp3
- source/{bookId}/original.txt
- logs/app-{date}.log

## Error Handling（错误处理）

分层策略：
- 前端：可恢复的错误以toast/inline提示并支持重试；致命错误（后端不可用）显示修复引导（重启后端/检查端口/查看日志）。
- 后端：
  - 输入校验错误：422 + 细粒度字段信息；
  - TTS调用错误：分类（限额、网络、语法）、自动降级与重试（指数退避，上限3次）；
  - 资源限制：磁盘空间不足/文件锁冲突时，失败并返回可操作建议；
  - 全局异常中间件：包含traceId与简化可读信息。
- 日志与可观测：结构化日志（bookId, chapterId, taskId），按日切分文件；关键路径打点（导入、切分、合成、播放）。

## Testing Strategy（测试策略）

- 单元：
  - 文本切分与预处理规则；
  - TTS适配器的参数映射与错误分支；
  - 数据库DAO与事务。
- 集成：
  - 导入→预处理→合成→播放完整链路（使用小样本文本与本地TTS）。
  - 云引擎模拟：超时/限额/网络失败→本地降级验证。
- 端到端（可选）：
  - Electron应用启动与核心交互流程冒烟。
- 性能基准：
  - 50MB文本导入耗时、1小时书籍合成吞吐、播放跳转延迟（≤500ms）。

设计决策与理由：
- 采用SQLite：部署简单、事务一致性、桌面应用天然契合。
- Electron + FastAPI解耦：充分利用Python在文本/TTS生态优势，同时保持前端现代化开发体验。
- 分章节落地与段内切片：平衡跳转体验与磁盘占用；并支撑失败恢复与断点重试。