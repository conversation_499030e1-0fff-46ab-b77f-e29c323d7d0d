# -*- coding: utf-8 -*-
"""/import 路由：导入本地TXT文件
- 功能：自动编码检测（BOM→UTF-8→GBK），采样换行风格分析，入库books
- 仅记录元数据，不修改源文件
"""
from __future__ import annotations
from pathlib import Path
from typing import Optional

from fastapi import APIRouter, HTTPException, Query
from pydantic import BaseModel
from sqlalchemy.orm import Session

from ..db.session import SessionLocal
from ..db.models import Book
from ..utils.text_detect import detect_encoding_and_newlines, get_file_size, guess_title

router = APIRouter()


class ImportResponse(BaseModel):
    bookId: int
    meta: dict


def get_db() -> Session:
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


@router.post("/import", response_model=ImportResponse, tags=["import"])
async def import_txt(
    filePath: str = Query(..., description="本地TXT文件绝对或相对路径"),
    encodingOverride: Optional[str] = Query(None, description="手动覆盖检测到的编码，例如 UTF-8/GBK"),
):
    p = Path(filePath)
    if not p.exists() or not p.is_file():
        raise HTTPException(status_code=400, detail="文件不存在或不是常规文件")

    # 检测编码与换行风格
    try:
        detected = detect_encoding_and_newlines(p)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"编码/换行检测失败: {e}")

    encoding = encodingOverride or detected["encoding"]
    size_bytes = get_file_size(p)
    title = guess_title(p)

    # 入库
    try:
        from fastapi import Depends
        db_gen = get_db()
        db = next(db_gen)
        book = Book(title=title, file_path=str(p.resolve()), encoding=encoding, size_bytes=size_bytes)
        db.add(book)
        db.commit()
        db.refresh(book)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"数据库写入失败: {e}")
    finally:
        try:
            next(db_gen)  # 触发 finally 关闭
        except StopIteration:
            pass

    return ImportResponse(
        bookId=book.id,
        meta={
            "encoding": encoding,
            "sizeBytes": size_bytes,
            "newline": detected.get("newline"),
            "bom": detected.get("bom"),
            "path": str(p.resolve()),
            "title": title,
        },
    )

