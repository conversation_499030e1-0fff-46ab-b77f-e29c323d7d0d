# 一键打包为单 exe（Windows PowerShell）
# 依赖：pip install pyinstaller
# 可选打包依赖：requirements.txt 中的可选项按需安装

param(
  [string]$Entry = "tts_reader.py",
  [string]$Name = "TTSReader"
)

$ErrorActionPreference = "Stop"

if (-not (Get-Command pyinstaller -ErrorAction SilentlyContinue)) {
  Write-Host "未检测到 pyinstaller，正在安装..." -ForegroundColor Yellow
  pip install pyinstaller
}

# 清理旧构建
Remove-Item -Recurse -Force build, dist -ErrorAction SilentlyContinue | Out-Null
Remove-Item -Force "$Name.spec" -ErrorAction SilentlyContinue | Out-Null

# 生成 exe
pyinstaller --noconfirm --clean --onefile --name $Name --add-data "README.md;." $Entry

Write-Host "\n打包完成：dist/$Name.exe" -ForegroundColor Green
Write-Host "提示：首次运行 EXE 可能会被 Windows 提示风险，点击 仍要运行。" -ForegroundColor Yellow

