# -*- coding: utf-8 -*-
"""
批量合成路由
- 提供批量预合成API
- 任务状态查询
- 队列管理
"""
from typing import List, Optional
from fastapi import APIRouter, HTTPException, Query, Depends
from sqlalchemy.orm import Session
import uuid
from pathlib import Path

from ..db.session import SessionLocal
from ..db.models import Book, Chapter
from ..services.batch_synthesis import (
    batch_synthesizer, 
    SynthesisTask,
    TaskStatus
)

router = APIRouter()


def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


@router.post("/batch/synthesize/{book_id}", tags=["batch"])
async def batch_synthesize_book(
    book_id: int,
    chapter_ids: Optional[List[int]] = Query(None, description="指定章节ID列表，不指定则合成全部"),
    engine: str = Query("local", description="TTS引擎: local或cloud"),
    voice: Optional[str] = Query(None, description="语音名称（云端TTS）"),
    rate: Optional[int] = Query(200, description="语速"),
    db: Session = Depends(get_db)
):
    """批量合成书籍章节"""
    # 验证书籍存在
    book = db.query(Book).filter(Book.id == book_id).first()
    if not book:
        raise HTTPException(status_code=404, detail="书籍不存在")
    
    # 获取章节列表
    query = db.query(Chapter).filter(Chapter.book_id == book_id)
    if chapter_ids:
        query = query.filter(Chapter.id.in_(chapter_ids))
    chapters = query.order_by(Chapter.idx).all()
    
    if not chapters:
        raise HTTPException(status_code=404, detail="没有找到章节")
    
    # 启动批量合成服务（如果未启动）
    if not batch_synthesizer.running:
        batch_synthesizer.start()
    
    # 创建合成任务
    tasks = []
    for chapter in chapters:
        # 跳过已缓存的章节
        if chapter.cached_path and Path(chapter.cached_path).exists():
            continue
        
        # 读取章节文本
        text = _read_text_segment(
            Path(book.file_path), 
            book.encoding, 
            chapter.start_offset, 
            chapter.end_offset
        )
        
        # 创建任务
        task = SynthesisTask(
            task_id=str(uuid.uuid4()),
            book_id=book_id,
            chapter_id=chapter.id,
            chapter_idx=chapter.idx,
            text=text,
            engine=engine,
            voice=voice,
            rate=rate
        )
        tasks.append(task)
    
    if not tasks:
        return {
            "message": "所有章节已缓存",
            "task_count": 0
        }
    
    # 添加到队列
    task_ids = batch_synthesizer.add_batch_tasks(tasks)
    
    return {
        "message": f"已添加 {len(task_ids)} 个合成任务到队列",
        "task_ids": task_ids,
        "task_count": len(task_ids)
    }


@router.get("/batch/status", tags=["batch"])
async def get_batch_status():
    """获取批量合成队列状态"""
    return batch_synthesizer.get_queue_status()


@router.get("/batch/task/{task_id}", tags=["batch"])
async def get_task_status(task_id: str):
    """获取单个任务状态"""
    task = batch_synthesizer.get_task_status(task_id)
    if not task:
        raise HTTPException(status_code=404, detail="任务不存在")
    
    return {
        "task_id": task.task_id,
        "status": task.status.value,
        "progress": task.progress,
        "error_message": task.error_message,
        "created_at": task.created_at.isoformat() if task.created_at else None,
        "completed_at": task.completed_at.isoformat() if task.completed_at else None,
        "output_path": str(task.output_path) if task.output_path else None
    }


@router.post("/batch/cancel/{task_id}", tags=["batch"])
async def cancel_task(task_id: str):
    """取消任务"""
    success = batch_synthesizer.cancel_task(task_id)
    if not success:
        raise HTTPException(status_code=404, detail="任务不存在或无法取消")
    
    return {"message": "任务已取消", "task_id": task_id}


@router.post("/batch/clear", tags=["batch"])
async def clear_completed_tasks():
    """清理已完成的任务记录"""
    batch_synthesizer.clear_completed_tasks()
    return {"message": "已清理完成任务记录"}


@router.post("/batch/start", tags=["batch"])
async def start_batch_service():
    """启动批量合成服务"""
    if batch_synthesizer.running:
        return {"message": "服务已在运行"}
    
    batch_synthesizer.start()
    return {"message": "批量合成服务已启动"}


@router.post("/batch/stop", tags=["batch"])
async def stop_batch_service():
    """停止批量合成服务"""
    if not batch_synthesizer.running:
        return {"message": "服务未运行"}
    
    batch_synthesizer.stop()
    return {"message": "批量合成服务已停止"}


def _read_text_segment(path: Path, encoding: Optional[str], start: Optional[int], end: Optional[int]) -> str:
    """读取文本片段"""
    text = path.read_text(encoding=encoding or "utf-8", errors="ignore")
    s = start or 0
    e = end or len(text)
    return text[s:e]
