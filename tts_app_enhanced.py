#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TXT→TTS 听书系统 - 增强版独立应用
集成云端TTS、批量合成等所有增强功能
"""
import os
import sys
import threading
import webbrowser
import tempfile
import shutil
from pathlib import Path
from typing import Optional, List, Dict, Any
import time
import json
import re
import logging
import asyncio
import uuid
from datetime import datetime
from enum import Enum
import queue

# 禁用日志输出
logging.getLogger().setLevel(logging.CRITICAL)

# Web框架
from fastapi import FastAPI, HTTPException, UploadFile, File, Query
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse, FileResponse
import uvicorn
import pyttsx3

# 尝试导入edge-tts
try:
    import edge_tts
    HAS_EDGE_TTS = True
except ImportError:
    HAS_EDGE_TTS = False
    print("[WARNING] edge-tts未安装，云端TTS功能不可用。安装命令: pip install edge-tts")

# 数据库
import sqlite3
from contextlib import contextmanager

# 音频处理
try:
    from pydub import AudioSegment
    HAS_PYDUB = True
except ImportError:
    HAS_PYDUB = False

# 文档处理
try:
    import docx
    HAS_DOCX = True
except ImportError:
    HAS_DOCX = False

try:
    import PyPDF2
    HAS_PDF = True
except ImportError:
    HAS_PDF = False

try:
    import ebooklib
    from ebooklib import epub
    from bs4 import BeautifulSoup
    HAS_EPUB = True
except ImportError:
    HAS_EPUB = False


class TaskStatus(Enum):
    """任务状态"""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"


class TTSEnhancedApp:
    def __init__(self):
        self.app = FastAPI(title="TTS听书系统增强版")
        self.cache_dir = Path(tempfile.gettempdir()) / "tts_app_cache"
        self.cache_dir.mkdir(exist_ok=True)
        self.db_path = self.cache_dir / "tts_books.db"
        
        # 批量合成任务队列
        self.batch_tasks = {}
        self.task_queue = queue.Queue()
        self.batch_worker = None
        
        # 初始化
        self.setup_database()
        self.setup_routes()
        self.app.mount("/audio-files", StaticFiles(directory=str(self.cache_dir)), name="audio-files")
        
        print("[INFO] TTS听书系统增强版启动中...")
        print("[INFO] 访问地址: http://127.0.0.1:8080")
        print(f"[INFO] 云端TTS: {'已启用' if HAS_EDGE_TTS else '未安装'}")
        print(f"[INFO] 支持格式: TXT, DOCX{'[OK]' if HAS_DOCX else '[X]'}, PDF{'[OK]' if HAS_PDF else '[X]'}, EPUB{'[OK]' if HAS_EPUB else '[X]'}")
        
        # 启动批量合成工作线程
        self.start_batch_worker()
        
        # 自动打开浏览器
        threading.Timer(2.0, lambda: webbrowser.open("http://127.0.0.1:8080")).start()
    
    def setup_database(self):
        """初始化数据库"""
        with self.get_db() as conn:
            conn.execute("""
                CREATE TABLE IF NOT EXISTS books (
                    id INTEGER PRIMARY KEY,
                    title TEXT,
                    file_path TEXT,
                    encoding TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            conn.execute("""
                CREATE TABLE IF NOT EXISTS chapters (
                    id INTEGER PRIMARY KEY,
                    book_id INTEGER,
                    idx INTEGER,
                    title TEXT,
                    start_offset INTEGER,
                    end_offset INTEGER,
                    cached_path TEXT,
                    tts_engine TEXT DEFAULT 'local',
                    FOREIGN KEY (book_id) REFERENCES books (id)
                )
            """)
            conn.execute("""
                CREATE TABLE IF NOT EXISTS settings (
                    key TEXT PRIMARY KEY,
                    value TEXT
                )
            """)
            conn.execute("""
                CREATE TABLE IF NOT EXISTS playback_progress (
                    book_id INTEGER PRIMARY KEY,
                    chapter_id INTEGER,
                    position REAL DEFAULT 0,
                    last_played TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (book_id) REFERENCES books (id),
                    FOREIGN KEY (chapter_id) REFERENCES chapters (id)
                )
            """)
    
    @contextmanager
    def get_db(self):
        """数据库连接"""
        conn = sqlite3.connect(str(self.db_path), check_same_thread=False)
        conn.row_factory = sqlite3.Row
        try:
            yield conn
            conn.commit()
        except Exception:
            conn.rollback()
            raise
        finally:
            conn.close()
    
    def detect_encoding(self, file_path: Path) -> str:
        """检测文件编码"""
        encodings = ['utf-8', 'gbk', 'gb2312', 'utf-16']
        for encoding in encodings:
            try:
                with open(file_path, 'r', encoding=encoding) as f:
                    f.read(1024)
                return encoding
            except:
                continue
        return 'utf-8'
    
    def extract_text_from_file(self, file_path: Path, file_type: str) -> str:
        """从文件提取文本"""
        try:
            if file_type == 'txt':
                encoding = self.detect_encoding(file_path)
                with open(file_path, 'r', encoding=encoding) as f:
                    return f.read()
            elif file_type == 'docx' and HAS_DOCX:
                doc = docx.Document(file_path)
                return '\n'.join([p.text for p in doc.paragraphs])
            elif file_type == 'pdf' and HAS_PDF:
                text = ""
                with open(file_path, 'rb') as f:
                    reader = PyPDF2.PdfReader(f)
                    for page in reader.pages:
                        text += page.extract_text() + '\n'
                return text
            elif file_type == 'epub' and HAS_EPUB:
                book = epub.read_epub(file_path)
                text = ""
                for item in book.get_items():
                    if item.get_type() == ebooklib.ITEM_DOCUMENT:
                        soup = BeautifulSoup(item.get_content(), 'html.parser')
                        text += soup.get_text() + '\n'
                return text
            else:
                raise ValueError(f"不支持的格式: {file_type}")
        except Exception as e:
            raise ValueError(f"文件解析失败: {str(e)}")
    
    def split_chapters(self, text: str, regex_pattern: str = None) -> List[Dict]:
        """分割章节"""
        if not regex_pattern:
            regex_pattern = r'^(第[一二三四五六七八九十百千万0-9]+章.*?)$'
        
        try:
            pattern = re.compile(regex_pattern, re.MULTILINE)
            matches = list(pattern.finditer(text))
            
            if not matches:
                chunk_size = 2000
                chapters = []
                for i in range(0, len(text), chunk_size):
                    chapters.append({
                        'title': f'第{len(chapters)+1}段',
                        'start_offset': i,
                        'end_offset': min(i + chunk_size, len(text))
                    })
                return chapters
            
            chapters = []
            for i, match in enumerate(matches):
                start = match.start()
                end = matches[i+1].start() if i+1 < len(matches) else len(text)
                chapters.append({
                    'title': match.group(1).strip(),
                    'start_offset': start,
                    'end_offset': end
                })
            return chapters
        except:
            return self.split_chapters(text, None)
    
    def synthesize_audio_local(self, text: str, output_path: Path, rate: int = 200) -> bool:
        """本地TTS合成"""
        try:
            engine = pyttsx3.init()
            voices = engine.getProperty('voices')
            for voice in voices:
                if 'chinese' in voice.name.lower() or 'huihui' in voice.name.lower():
                    engine.setProperty('voice', voice.id)
                    break
            
            engine.setProperty('rate', rate)
            engine.setProperty('volume', 1.0)
            
            output_path.parent.mkdir(parents=True, exist_ok=True)
            if output_path.exists():
                output_path.unlink()
            
            engine.save_to_file(text, str(output_path))
            engine.runAndWait()
            
            return output_path.exists() and output_path.stat().st_size > 100
        except Exception as e:
            print(f"[ERROR] 本地TTS合成失败: {e}")
            return False
    
    async def synthesize_audio_cloud(self, text: str, output_path: Path, 
                                    voice: str = "zh-CN-XiaoxiaoNeural", 
                                    rate: str = "+0%") -> bool:
        """云端TTS合成"""
        if not HAS_EDGE_TTS:
            return False
        
        try:
            output_path.parent.mkdir(parents=True, exist_ok=True)
            communicate = edge_tts.Communicate(text, voice, rate=rate)
            await communicate.save(str(output_path))
            return output_path.exists() and output_path.stat().st_size > 100
        except Exception as e:
            print(f"[ERROR] 云端TTS合成失败: {e}")
            return False
    
    def start_batch_worker(self):
        """启动批量合成工作线程"""
        def worker():
            while True:
                try:
                    task = self.task_queue.get(timeout=1)
                    if task is None:
                        break
                    
                    task_id = task['id']
                    self.batch_tasks[task_id]['status'] = TaskStatus.PROCESSING
                    
                    # 执行合成
                    success = self.process_batch_task(task)
                    
                    if success:
                        self.batch_tasks[task_id]['status'] = TaskStatus.COMPLETED
                    else:
                        self.batch_tasks[task_id]['status'] = TaskStatus.FAILED
                    
                    self.batch_tasks[task_id]['completed_at'] = datetime.now().isoformat()
                    
                except queue.Empty:
                    continue
                except Exception as e:
                    print(f"[ERROR] 批量合成错误: {e}")
        
        self.batch_worker = threading.Thread(target=worker, daemon=True)
        self.batch_worker.start()
    
    def process_batch_task(self, task) -> bool:
        """处理批量合成任务"""
        try:
            book_id = task['book_id']
            chapter_id = task['chapter_id']
            text = task['text']
            engine = task['engine']
            
            if engine == 'cloud':
                output_path = self.cache_dir / f"ch_{chapter_id}_cloud.mp3"
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                success = loop.run_until_complete(
                    self.synthesize_audio_cloud(text, output_path, task.get('voice', 'zh-CN-XiaoxiaoNeural'))
                )
            else:
                output_path = self.cache_dir / f"ch_{chapter_id}.wav"
                success = self.synthesize_audio_local(text, output_path, task.get('rate', 200))
            
            if success:
                with self.get_db() as conn:
                    conn.execute(
                        "UPDATE chapters SET cached_path = ?, tts_engine = ? WHERE id = ?",
                        (str(output_path), engine, chapter_id)
                    )
            
            return success
        except Exception as e:
            print(f"[ERROR] 任务处理失败: {e}")
            return False
    
    def setup_routes(self):
        """设置API路由"""
        
        @self.app.get("/", response_class=HTMLResponse)
        async def index():
            return self.get_html_interface()
        
        @self.app.post("/import")
        async def import_book(file: UploadFile = File(...)):
            """导入文档"""
            try:
                file_ext = file.filename.lower().split('.')[-1]
                supported = ['txt', 'docx', 'pdf', 'epub']
                
                if file_ext not in supported:
                    raise HTTPException(400, detail=f"仅支持 {', '.join(supported)}")
                
                temp_path = self.cache_dir / f"book_{int(time.time())}.{file_ext}"
                with open(temp_path, 'wb') as f:
                    content = await file.read()
                    f.write(content)
                
                text_content = self.extract_text_from_file(temp_path, file_ext)
                if not text_content.strip():
                    raise HTTPException(400, detail="文件内容为空")
                
                with self.get_db() as conn:
                    cursor = conn.execute(
                        "INSERT INTO books (title, file_path, encoding) VALUES (?, ?, ?)",
                        (file.filename, str(temp_path), file_ext)
                    )
                    book_id = cursor.lastrowid
                
                return {"bookId": book_id, "title": file.filename}
            except HTTPException:
                raise
            except Exception as e:
                raise HTTPException(500, detail=str(e))
        
        @self.app.post("/preprocess/{book_id}")
        async def preprocess_book(book_id: int, regex_pattern: str = None):
            """预处理分章"""
            with self.get_db() as conn:
                book = conn.execute("SELECT * FROM books WHERE id = ?", (book_id,)).fetchone()
                if not book:
                    raise HTTPException(404, detail="书籍不存在")
                
                text = self.extract_text_from_file(Path(book['file_path']), book['encoding'])
                chapters = self.split_chapters(text, regex_pattern)
                
                conn.execute("DELETE FROM chapters WHERE book_id = ?", (book_id,))
                
                for i, chapter in enumerate(chapters):
                    conn.execute(
                        "INSERT INTO chapters (book_id, idx, title, start_offset, end_offset) VALUES (?, ?, ?, ?, ?)",
                        (book_id, i+1, chapter['title'], chapter['start_offset'], chapter['end_offset'])
                    )
                
                return {"bookId": book_id, "chapters": len(chapters)}
        
        @self.app.get("/chapters/{book_id}")
        async def get_chapters(book_id: int):
            """获取章节列表"""
            with self.get_db() as conn:
                chapters = conn.execute(
                    "SELECT id, idx, title, cached_path FROM chapters WHERE book_id = ? ORDER BY idx",
                    (book_id,)
                ).fetchall()
                return [
                    {
                        "id": ch['id'],
                        "idx": ch['idx'],
                        "title": ch['title'],
                        "cached": bool(ch['cached_path'])
                    }
                    for ch in chapters
                ]
        
        @self.app.post("/synthesize/{book_id}/{chapter_id}")
        async def synthesize_chapter(
            book_id: int, 
            chapter_id: int, 
            engine: str = Query("local", description="TTS引擎: local或cloud"),
            voice: str = Query("zh-CN-XiaoxiaoNeural", description="云端语音"),
            rate: int = Query(200, description="语速")
        ):
            """合成单个章节"""
            with self.get_db() as conn:
                book = conn.execute("SELECT * FROM books WHERE id = ?", (book_id,)).fetchone()
                chapter = conn.execute("SELECT * FROM chapters WHERE id = ? AND book_id = ?", 
                                      (chapter_id, book_id)).fetchone()
                
                if not book or not chapter:
                    raise HTTPException(404, detail="书籍或章节不存在")
                
                # 检查缓存
                if chapter['cached_path'] and Path(chapter['cached_path']).exists():
                    audio_filename = Path(chapter['cached_path']).name
                    return {"audioPath": f"/audio-files/{audio_filename}", "cached": True}
                
                # 提取文本
                full_text = self.extract_text_from_file(Path(book['file_path']), book['encoding'])
                text = full_text[chapter['start_offset']:chapter['end_offset']]
                
                if not text.strip():
                    raise HTTPException(400, detail="章节内容为空")
                
                # 检查是否强制重新生成（根据文件名中的语速判断）
                force_regen = False
                
                # 如果有已缓存文件但语速不同，需要重新生成
                if chapter['cached_path'] and Path(chapter['cached_path']).exists():
                    cached_name = Path(chapter['cached_path']).name
                    # 检查缓存文件名中的语速是否匹配
                    if f"_{rate}" not in cached_name:
                        force_regen = True
                
                # 合成音频
                if engine == "cloud" and HAS_EDGE_TTS:
                    output_path = self.cache_dir / f"ch_{chapter_id}_cloud_{rate}.mp3"
                    if force_regen and output_path.exists():
                        output_path.unlink()  # 删除旧文件
                    success = await self.synthesize_audio_cloud(text, output_path, voice)
                else:
                    output_path = self.cache_dir / f"ch_{chapter_id}_{rate}.wav"
                    if force_regen and output_path.exists():
                        output_path.unlink()  # 删除旧文件
                    success = self.synthesize_audio_local(text, output_path, rate)
                
                if success:
                    conn.execute("UPDATE chapters SET cached_path = ?, tts_engine = ? WHERE id = ?", 
                               (str(output_path), engine, chapter_id))
                    return {"audioPath": f"/audio-files/{output_path.name}", "cached": False}
                else:
                    raise HTTPException(500, detail="音频合成失败")
        
        @self.app.post("/batch/synthesize/{book_id}")
        async def batch_synthesize(
            book_id: int,
            engine: str = Query("local", description="TTS引擎"),
            voice: str = Query("zh-CN-XiaoxiaoNeural", description="云端语音")
        ):
            """批量合成所有章节"""
            with self.get_db() as conn:
                book = conn.execute("SELECT * FROM books WHERE id = ?", (book_id,)).fetchone()
                if not book:
                    raise HTTPException(404, detail="书籍不存在")
                
                chapters = conn.execute(
                    "SELECT * FROM chapters WHERE book_id = ? ORDER BY idx",
                    (book_id,)
                ).fetchall()
                
                if not chapters:
                    raise HTTPException(404, detail="没有章节")
                
                # 读取完整文本
                full_text = self.extract_text_from_file(Path(book['file_path']), book['encoding'])
                
                # 创建批量任务
                task_ids = []
                for chapter in chapters:
                    # 跳过已缓存的
                    if chapter['cached_path'] and Path(chapter['cached_path']).exists():
                        continue
                    
                    task_id = str(uuid.uuid4())
                    text = full_text[chapter['start_offset']:chapter['end_offset']]
                    
                    task = {
                        'id': task_id,
                        'book_id': book_id,
                        'chapter_id': chapter['id'],
                        'text': text,
                        'engine': engine,
                        'voice': voice,
                        'created_at': datetime.now().isoformat()
                    }
                    
                    self.batch_tasks[task_id] = {
                        'status': TaskStatus.PENDING,
                        'chapter_title': chapter['title'],
                        'created_at': task['created_at']
                    }
                    
                    self.task_queue.put(task)
                    task_ids.append(task_id)
                
                return {
                    "message": f"已添加 {len(task_ids)} 个合成任务",
                    "task_ids": task_ids
                }
        
        @self.app.get("/batch/status")
        async def get_batch_status():
            """获取批量任务状态"""
            status_count = {
                TaskStatus.PENDING: 0,
                TaskStatus.PROCESSING: 0,
                TaskStatus.COMPLETED: 0,
                TaskStatus.FAILED: 0
            }
            
            for task_id, task in self.batch_tasks.items():
                status_count[task['status']] += 1
            
            return {
                "pending": status_count[TaskStatus.PENDING],
                "processing": status_count[TaskStatus.PROCESSING],
                "completed": status_count[TaskStatus.COMPLETED],
                "failed": status_count[TaskStatus.FAILED],
                "total": len(self.batch_tasks)
            }
        
        @self.app.get("/voices")
        async def get_voices():
            """获取可用语音列表"""
            voices = {
                "local": {
                    "available": True,
                    "voices": [{"name": "系统默认", "value": "default"}]
                },
                "cloud": {
                    "available": HAS_EDGE_TTS,
                    "voices": []
                }
            }
            
            if HAS_EDGE_TTS:
                voices["cloud"]["voices"] = [
                    {"name": "晓晓", "value": "zh-CN-XiaoxiaoNeural", "gender": "女"},
                    {"name": "云希", "value": "zh-CN-YunxiNeural", "gender": "男"},
                    {"name": "晓伊", "value": "zh-CN-XiaoyiNeural", "gender": "女"},
                    {"name": "云扬", "value": "zh-CN-YunyangNeural", "gender": "男"},
                ]
            
            return voices
        
        @self.app.get("/audio/{book_id}/{chapter_id}")
        async def get_audio(book_id: int, chapter_id: int):
            """获取音频文件"""
            with self.get_db() as conn:
                chapter = conn.execute(
                    "SELECT cached_path FROM chapters WHERE id = ? AND book_id = ?",
                    (chapter_id, book_id)
                ).fetchone()
                
                if not chapter or not chapter['cached_path']:
                    raise HTTPException(404, detail="音频文件不存在")
                
                audio_path = Path(chapter['cached_path'])
                if not audio_path.exists():
                    raise HTTPException(404, detail="音频文件不存在")
                
                media_type = "audio/mpeg" if str(audio_path).endswith('.mp3') else "audio/wav"
                return FileResponse(audio_path, media_type=media_type)
        
        @self.app.post("/progress/save")
        async def save_progress(
            book_id: int = Query(..., description="书籍ID"),
            chapter_id: int = Query(..., description="章节ID"),
            position: float = Query(..., description="播放位置（秒）")
        ):
            """保存播放进度"""
            with self.get_db() as conn:
                # 检查是否已有记录
                existing = conn.execute(
                    "SELECT book_id FROM playback_progress WHERE book_id = ?",
                    (book_id,)
                ).fetchone()
                
                if existing:
                    conn.execute(
                        "UPDATE playback_progress SET chapter_id = ?, position = ?, last_played = CURRENT_TIMESTAMP WHERE book_id = ?",
                        (chapter_id, position, book_id)
                    )
                else:
                    conn.execute(
                        "INSERT INTO playback_progress (book_id, chapter_id, position) VALUES (?, ?, ?)",
                        (book_id, chapter_id, position)
                    )
                
                return {"status": "success", "message": "进度已保存"}
        
        @self.app.get("/progress/{book_id}")
        async def get_progress(book_id: int):
            """获取播放进度"""
            with self.get_db() as conn:
                progress = conn.execute(
                    """SELECT p.*, c.title as chapter_title, c.idx as chapter_idx
                       FROM playback_progress p
                       LEFT JOIN chapters c ON p.chapter_id = c.id
                       WHERE p.book_id = ?""",
                    (book_id,)
                ).fetchone()
                
                if progress:
                    return {
                        "bookId": progress['book_id'],
                        "chapterId": progress['chapter_id'],
                        "chapterTitle": progress['chapter_title'],
                        "chapterIdx": progress['chapter_idx'],
                        "position": progress['position'],
                        "lastPlayed": progress['last_played']
                    }
                else:
                    return None
        
        @self.app.get("/books")
        async def get_books():
            """获取所有书籍列表"""
            with self.get_db() as conn:
                books = conn.execute(
                    """SELECT b.*, COUNT(c.id) as chapter_count,
                       SUM(CASE WHEN c.cached_path IS NOT NULL THEN 1 ELSE 0 END) as cached_count
                       FROM books b
                       LEFT JOIN chapters c ON b.id = c.book_id
                       GROUP BY b.id
                       ORDER BY b.created_at DESC"""
                ).fetchall()
                
                result = []
                for book in books:
                    # 获取进度信息
                    progress = conn.execute(
                        "SELECT * FROM playback_progress WHERE book_id = ?",
                        (book['id'],)
                    ).fetchone()
                    
                    result.append({
                        "id": book['id'],
                        "title": book['title'],
                        "chapterCount": book['chapter_count'] or 0,
                        "cachedCount": book['cached_count'] or 0,
                        "createdAt": book['created_at'],
                        "hasProgress": bool(progress)
                    })
                
                return result
        
        @self.app.delete("/books/{book_id}")
        async def delete_book(book_id: int):
            """删除书籍及相关数据"""
            with self.get_db() as conn:
                # 获取所有章节的缓存文件
                chapters = conn.execute(
                    "SELECT cached_path FROM chapters WHERE book_id = ? AND cached_path IS NOT NULL",
                    (book_id,)
                ).fetchall()
                
                # 删除缓存文件
                for chapter in chapters:
                    if chapter['cached_path']:
                        audio_path = Path(chapter['cached_path'])
                        if audio_path.exists():
                            audio_path.unlink()
                
                # 删除数据库记录
                conn.execute("DELETE FROM playback_progress WHERE book_id = ?", (book_id,))
                conn.execute("DELETE FROM chapters WHERE book_id = ?", (book_id,))
                conn.execute("DELETE FROM books WHERE id = ?", (book_id,))
                
                return {"status": "success", "message": "书籍已删除"}
        
        @self.app.get("/export/{book_id}")
        async def export_book(book_id: int, format: str = Query("mp3", description="导出格式")):
            """导出书籍音频文件"""
            import zipfile
            from io import BytesIO
            from fastapi.responses import StreamingResponse
            
            with self.get_db() as conn:
                book = conn.execute("SELECT * FROM books WHERE id = ?", (book_id,)).fetchone()
                if not book:
                    raise HTTPException(404, detail="书籍不存在")
                
                chapters = conn.execute(
                    "SELECT * FROM chapters WHERE book_id = ? AND cached_path IS NOT NULL ORDER BY idx",
                    (book_id,)
                ).fetchall()
                
                if not chapters:
                    raise HTTPException(400, detail="没有可导出的音频文件")
                
                # 创建ZIP文件
                zip_buffer = BytesIO()
                with zipfile.ZipFile(zip_buffer, 'w', zipfile.ZIP_DEFLATED) as zip_file:
                    for chapter in chapters:
                        audio_path = Path(chapter['cached_path'])
                        if audio_path.exists():
                            # 生成文件名
                            file_ext = audio_path.suffix
                            file_name = f"{chapter['idx']:03d}_{chapter['title']}{file_ext}"
                            # 清理文件名中的非法字符
                            file_name = re.sub(r'[<>:"/\\|?*]', '_', file_name)
                            
                            # 如果需要转换格式（需要pydub）
                            if format.lower() == 'mp3' and file_ext != '.mp3' and HAS_PYDUB:
                                audio = AudioSegment.from_file(str(audio_path))
                                temp_mp3 = self.cache_dir / f"temp_{chapter['id']}.mp3"
                                audio.export(str(temp_mp3), format="mp3")
                                zip_file.write(str(temp_mp3), file_name.replace(file_ext, '.mp3'))
                                temp_mp3.unlink()
                            else:
                                zip_file.write(str(audio_path), file_name)
                
                zip_buffer.seek(0)
                
                # 生成下载文件名
                download_name = f"{book['title']}_音频合集.zip"
                download_name = re.sub(r'[<>:"/\\|?*]', '_', download_name)
                
                return StreamingResponse(
                    zip_buffer,
                    media_type="application/zip",
                    headers={"Content-Disposition": f"attachment; filename*=UTF-8''{download_name}"}
                )
    
    def get_html_interface(self) -> str:
        """返回增强版HTML界面"""
        return """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TTS听书系统 - 增强版</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: 'Microsoft YaHei', sans-serif; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container { 
            max-width: 1200px; 
            margin: 20px auto; 
            background: white; 
            border-radius: 16px; 
            box-shadow: 0 20px 60px rgba(0,0,0,0.3); 
            overflow: hidden;
        }
        .header { 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 { font-size: 2em; margin-bottom: 10px; }
        .header p { opacity: 0.9; }
        
        .main-content { padding: 30px; }
        
        .upload-area { 
            border: 3px dashed #667eea; 
            padding: 60px; 
            text-align: center; 
            margin-bottom: 30px; 
            border-radius: 12px;
            background: #f8f9ff;
            transition: all 0.3s;
            cursor: pointer;
        }
        .upload-area:hover { 
            border-color: #764ba2; 
            background: #f0f2ff;
        }
        
        .feature-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .feature-card {
            padding: 20px;
            border-radius: 8px;
            background: #f8f9ff;
            border: 1px solid #e0e0e0;
        }
        
        .feature-card h3 {
            color: #667eea;
            margin-bottom: 10px;
        }
        
        .btn { 
            padding: 12px 24px; 
            margin: 5px; 
            border: none; 
            border-radius: 6px; 
            cursor: pointer; 
            font-size: 14px;
            transition: all 0.3s;
            font-weight: 500;
        }
        .btn-primary { 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white; 
        }
        .btn-success { 
            background: linear-gradient(135deg, #56ab2f 0%, #a8e063 100%);
            color: white; 
        }
        .btn-warning {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
        }
        .btn:hover { 
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }
        
        .chapters { 
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 30px;
            margin-top: 30px;
        }
        
        .chapter-list { 
            max-height: 500px; 
            overflow-y: auto; 
            border: 1px solid #e0e0e0; 
            border-radius: 8px;
            background: #fafafa;
        }
        .chapter-item { 
            padding: 15px; 
            border-bottom: 1px solid #f0f0f0; 
            cursor: pointer;
            transition: all 0.3s;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .chapter-item:hover { 
            background: #f0f2ff; 
        }
        .chapter-item.active { 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white; 
        }
        .chapter-item .badge {
            font-size: 12px;
            padding: 2px 8px;
            border-radius: 12px;
            background: #4caf50;
            color: white;
        }
        .chapter-item .bookmark {
            font-size: 12px;
            padding: 2px 8px;
            border-radius: 12px;
            background: #ff9800;
            color: white;
            margin-left: 5px;
        }
        
        .player { 
            padding: 30px; 
            border: 1px solid #e0e0e0; 
            border-radius: 8px;
            background: #fafafa;
        }
        
        .engine-selector {
            margin: 20px 0;
            padding: 20px;
            background: #f8f9ff;
            border-radius: 8px;
        }
        
        .voice-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;
            margin-top: 10px;
        }
        
        .voice-option {
            padding: 10px;
            border: 2px solid #e0e0e0;
            border-radius: 6px;
            cursor: pointer;
            text-align: center;
            transition: all 0.3s;
        }
        
        .voice-option:hover {
            border-color: #667eea;
            background: #f0f2ff;
        }
        
        .voice-option.selected {
            border-color: #667eea;
            background: #667eea;
            color: white;
        }
        
        .progress { margin: 20px 0; }
        .progress label { display: block; margin-bottom: 5px; color: #666; }
        
        .status { 
            margin: 20px 0; 
            padding: 15px; 
            border-radius: 8px;
            font-weight: 500;
        }
        .status.info { 
            background: #e3f2fd; 
            color: #1976d2;
            border-left: 4px solid #1976d2;
        }
        .status.error { 
            background: #ffebee; 
            color: #c62828;
            border-left: 4px solid #c62828;
        }
        .status.success {
            background: #e8f5e9;
            color: #2e7d32;
            border-left: 4px solid #2e7d32;
        }
        
        audio { 
            width: 100%; 
            margin: 20px 0;
            border-radius: 8px;
        }
        
        input[type="file"] { display: none; }
        input[type="text"] {
            padding: 10px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            width: 100%;
        }
        input[type="range"] {
            width: 100%;
            margin: 10px 0;
        }
        
        .batch-status {
            padding: 20px;
            background: #f8f9ff;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 15px;
            margin-top: 15px;
        }
        
        .status-item {
            text-align: center;
            padding: 15px;
            background: white;
            border-radius: 6px;
            border: 1px solid #e0e0e0;
        }
        
        .status-item .number {
            font-size: 24px;
            font-weight: bold;
            color: #667eea;
        }
        
        .status-item .label {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
        
        /* 书籍管理样式 */
        .book-manager {
            margin-bottom: 30px;
            padding: 20px;
            background: #f8f9ff;
            border-radius: 12px;
        }
        
        .book-list {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        
        .book-card {
            padding: 15px;
            background: white;
            border-radius: 8px;
            border: 1px solid #e0e0e0;
            transition: all 0.3s;
            cursor: pointer;
        }
        
        .book-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .book-card.active {
            border-color: #667eea;
            background: linear-gradient(135deg, #f0f2ff 0%, #f8f9ff 100%);
        }
        
        .book-card-title {
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        
        .book-card-info {
            font-size: 12px;
            color: #666;
            margin: 5px 0;
        }
        
        .book-card-actions {
            display: flex;
            gap: 5px;
            margin-top: 10px;
        }
        
        .btn-small {
            padding: 6px 12px;
            font-size: 12px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .btn-danger {
            background: #dc3545;
            color: white;
        }
        
        .btn-info {
            background: #17a2b8;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎧 TTS听书系统 - 增强版</h1>
            <p>支持多种文档格式 | 云端语音合成 | 批量处理</p>
        </div>
        
        <div class="main-content">
            <!-- 书籍管理区域 -->
            <div id="bookManager" class="book-manager" style="display:none;">
                <h3>📚 我的书架</h3>
                <div class="book-list" id="bookList"></div>
            </div>
            
            <div class="upload-area" onclick="document.getElementById('fileInput').click()">
                <h2>📁 点击上传文档</h2>
                <p style="margin-top: 10px;">支持 TXT / DOCX / PDF / EPUB 格式</p>
                <p style="margin-top: 5px; font-size: 14px; opacity: 0.7;">或拖拽文件到此处</p>
                <input type="file" id="fileInput" accept=".txt,.docx,.pdf,.epub" onchange="uploadFile(this.files[0])">
            </div>
            
            <div id="status" class="status" style="display:none;"></div>
            
            <div id="bookInfo" style="display:none;">
                <h3 style="margin: 20px 0;">📖 <span id="bookTitle"></span></h3>
                <div style="display: flex; gap: 10px; align-items: center; margin-bottom: 15px;">
                    <input type="text" id="regexInput" placeholder="章节正则表达式（可选）" style="flex: 1;">
                    <button class="btn btn-primary" onclick="preprocessBook()">🔍 智能分章</button>
                </div>
                <div style="display: flex; gap: 10px;">
                    <button class="btn btn-info" onclick="exportBook()">📥 导出音频</button>
                    <button class="btn btn-primary" onclick="showBookList()">📚 书架</button>
                </div>
            </div>
            
            <div id="engineSettings" style="display:none;">
                <div class="engine-selector">
                    <h3>🎯 选择合成引擎</h3>
                    <div style="margin: 15px 0;">
                        <label style="margin-right: 20px;">
                            <input type="radio" name="engine" value="local" checked onchange="onEngineChange()">
                            本地引擎（快速）
                        </label>
                        <label id="cloudOption">
                            <input type="radio" name="engine" value="cloud" onchange="onEngineChange()">
                            云端引擎（高质量）
                        </label>
                    </div>
                    
                    <div id="voiceSelection" style="display:none;">
                        <h4 style="margin: 15px 0;">选择语音</h4>
                        <div class="voice-grid" id="voiceGrid"></div>
                    </div>
                    
                    <div class="progress">
                        <label>语速: <span id="rateValue">200</span></label>
                        <input type="range" id="rateSlider" min="100" max="300" value="200" oninput="updateRate(this.value)">
                    </div>
                    
                    <div style="margin-top: 20px;">
                        <button class="btn btn-warning" onclick="batchSynthesize()">⚡ 批量合成所有章节</button>
                        <button class="btn btn-primary" onclick="checkBatchStatus()">📊 查看合成进度</button>
                    </div>
                </div>
            </div>
            
            <div id="batchStatus" class="batch-status" style="display:none;">
                <h3>批量合成进度</h3>
                <div class="status-grid">
                    <div class="status-item">
                        <div class="number" id="pendingCount">0</div>
                        <div class="label">等待中</div>
                    </div>
                    <div class="status-item">
                        <div class="number" id="processingCount">0</div>
                        <div class="label">处理中</div>
                    </div>
                    <div class="status-item">
                        <div class="number" id="completedCount">0</div>
                        <div class="label">已完成</div>
                    </div>
                    <div class="status-item">
                        <div class="number" id="failedCount">0</div>
                        <div class="label">失败</div>
                    </div>
                </div>
            </div>
            
            <div id="mainInterface" class="chapters" style="display:none;">
                <div>
                    <h3 style="margin-bottom: 15px;">📑 章节列表</h3>
                    <div class="chapter-list" id="chapterList"></div>
                </div>
                
                    <div class="player">
                    <h3>🎵 播放器</h3>
                    <div id="currentChapter" style="margin: 15px 0; font-size: 18px;">请选择章节</div>
                    <div id="progressInfo" style="margin: 10px 0; font-size: 14px; color: #666; display: none;">
                        <span>📍 上次播放位置: </span>
                        <span id="lastPosition"></span>
                        <button class="btn btn-primary" style="padding: 6px 12px; font-size: 12px; margin-left: 10px;" onclick="resumeFromBookmark()">
                            继续播放
                        </button>
                    </div>
                    <audio id="audioPlayer" controls style="display:none;"></audio>
                    <div style="margin-top: 20px;">
                        <button class="btn btn-success" onclick="synthesizeAndPlay()" id="playBtn" disabled>
                            🎤 合成并播放
                        </button>
                        <button class="btn btn-primary" onclick="playPause()" id="pauseBtn" disabled>
                            ⏸️ 暂停
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentBookId = null;
        let currentChapterId = null;
        let currentRate = 200;
        let lastSynthesizedRate = 200;  // 记录上次合成的语速
        let forceRegenerate = false;  // 是否强制重新生成
        let currentEngine = 'local';
        let currentVoice = 'zh-CN-XiaoxiaoNeural';
        let availableVoices = {};
        let saveProgressInterval = null;  // 保存进度的定时器
        let lastSavedProgress = null;  // 上次保存的进度
        
        // 初始化
        window.onload = async function() {
            await loadVoices();
            await loadBooks();
        };
        
        async function loadVoices() {
            try {
                const response = await fetch('/voices');
                availableVoices = await response.json();
                
                // 更新云端选项可用性
                if (!availableVoices.cloud.available) {
                    document.getElementById('cloudOption').style.opacity = '0.5';
                    document.getElementById('cloudOption').title = '云端TTS未安装';
                }
            } catch (error) {
                console.error('加载语音失败:', error);
            }
        }
        
        function onEngineChange() {
            currentEngine = document.querySelector('input[name="engine"]:checked').value;
            
            if (currentEngine === 'cloud' && availableVoices.cloud.available) {
                document.getElementById('voiceSelection').style.display = 'block';
                
                const voiceGrid = document.getElementById('voiceGrid');
                voiceGrid.innerHTML = availableVoices.cloud.voices.map(voice => `
                    <div class="voice-option ${voice.value === currentVoice ? 'selected' : ''}" 
                         onclick="selectVoice('${voice.value}', this)">
                        <div>${voice.name}</div>
                        <small>${voice.gender || ''}</small>
                    </div>
                `).join('');
            } else {
                document.getElementById('voiceSelection').style.display = 'none';
            }
        }
        
        function selectVoice(voice, element) {
            currentVoice = voice;
            document.querySelectorAll('.voice-option').forEach(el => el.classList.remove('selected'));
            element.classList.add('selected');
        }
        
        function showStatus(message, type = 'info') {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = `status ${type}`;
            status.style.display = 'block';
            
            if (type !== 'error') {
                setTimeout(() => status.style.display = 'none', 3000);
            }
        }
        
        async function uploadFile(file) {
            if (!file) return;
            
            showStatus('正在导入文件...', 'info');
            const formData = new FormData();
            formData.append('file', file);
            
            try {
                const response = await fetch('/import', {
                    method: 'POST',
                    body: formData
                });
                
                if (!response.ok) {
                    const error = await response.json();
                    showStatus(error.detail || '导入失败', 'error');
                    return;
                }
                
                const result = await response.json();
                currentBookId = result.bookId;
                document.getElementById('bookTitle').textContent = result.title;
                document.getElementById('bookInfo').style.display = 'block';
                showStatus('文件导入成功！', 'success');
            } catch (error) {
                showStatus('导入失败: ' + error.message, 'error');
            }
        }
        
        async function preprocessBook() {
            if (!currentBookId) return;
            
            showStatus('正在智能分章...', 'info');
            const regex = document.getElementById('regexInput').value;
            
            try {
                const url = `/preprocess/${currentBookId}${regex ? '?regex_pattern=' + encodeURIComponent(regex) : ''}`;
                const response = await fetch(url, { method: 'POST' });
                const result = await response.json();
                
                if (response.ok) {
                    showStatus(`分章完成，共 ${result.chapters} 章`, 'success');
                    await loadChapters();
                    document.getElementById('engineSettings').style.display = 'block';
                    document.getElementById('mainInterface').style.display = 'grid';
                    
                    // 加载播放进度
                    await loadProgress();
                } else {
                    showStatus(result.detail || '预处理失败', 'error');
                }
            } catch (error) {
                showStatus('预处理失败: ' + error.message, 'error');
            }
        }
        
        async function loadChapters() {
            if (!currentBookId) return;
            
            try {
                const response = await fetch(`/chapters/${currentBookId}`);
                const chapters = await response.json();
                
                const listEl = document.getElementById('chapterList');
                listEl.innerHTML = chapters.map(ch => `
                    <div class="chapter-item" onclick="selectChapter(${ch.id}, '${ch.title.replace(/'/g, "\\'")}')"
                         data-chapter-id="${ch.id}" data-cached="${ch.cached}">
                        <span>${ch.idx}. ${ch.title}</span>
                        <span id="bookmark-${ch.id}" style="display: none;" class="bookmark">📍</span>
                        ${ch.cached ? '<span class="badge">已缓存</span>' : ''}
                    </div>
                `).join('');
                
                // 显示书签标记
                if (lastSavedProgress && lastSavedProgress.chapterId) {
                    const bookmark = document.getElementById(`bookmark-${lastSavedProgress.chapterId}`);
                    if (bookmark) bookmark.style.display = 'inline-block';
                }
            } catch (error) {
                showStatus('加载章节失败: ' + error.message, 'error');
            }
        }
        
        function selectChapter(chapterId, title) {
            currentChapterId = chapterId;
            document.getElementById('currentChapter').textContent = `当前章节: ${title}`;
            document.getElementById('playBtn').disabled = false;
            
            // 停止当前播放
            const audioPlayer = document.getElementById('audioPlayer');
            if (!audioPlayer.paused) {
                audioPlayer.pause();
                audioPlayer.currentTime = 0;
            }
            
            // 更新选中状态
            document.querySelectorAll('.chapter-item').forEach(el => el.classList.remove('active'));
            event.target.closest('.chapter-item').classList.add('active');
            
            // 如果章节已缓存，自动加载音频
            const chapterItem = event.target.closest('.chapter-item');
            if (chapterItem && chapterItem.querySelector('.badge')) {
                // 已缓存，直接加载音频
                audioPlayer.src = `/audio/${currentBookId}/${currentChapterId}`;
                audioPlayer.style.display = 'block';
                document.getElementById('pauseBtn').disabled = false;
                showStatus('已加载缓存音频，点击播放按钮开始', 'success');
            }
        }
        
        async function synthesizeAndPlay() {
            if (!currentBookId || !currentChapterId) return;
            
            // 检查是否需要重新合成（语速改变）
            const currentItem = document.querySelector(`[data-chapter-id="${currentChapterId}"]`);
            const isCached = currentItem && currentItem.dataset.cached === 'true';
            
            // 如果已缓存且语速未改变，直接播放
            if (isCached && !forceRegenerate) {
                const audioPlayer = document.getElementById('audioPlayer');
                if (audioPlayer.src && audioPlayer.src.includes(`/audio/${currentBookId}/${currentChapterId}`)) {
                    audioPlayer.play();
                    document.getElementById('pauseBtn').disabled = false;
                    showStatus('继续播放', 'success');
                    return;
                }
            }
            
            showStatus('正在合成音频...', 'info');
            document.getElementById('playBtn').disabled = true;
            
            try {
                // 使用当前最新的语速值
                const params = new URLSearchParams({
                    engine: currentEngine,
                    voice: currentVoice,
                    rate: currentRate  // 确保使用最新的语速值
                });
                
                // 如果语速改变了，强制重新生成
                if (forceRegenerate) {
                    params.append('forceRegen', 'true');
                    forceRegenerate = false;  // 重置标志
                }
                
                const response = await fetch(
                    `/synthesize/${currentBookId}/${currentChapterId}?${params}`,
                    { method: 'POST' }
                );
                const result = await response.json();
                
                if (response.ok) {
                    const audioPlayer = document.getElementById('audioPlayer');
                    // 添加时间戳避免缓存
                    audioPlayer.src = `/audio/${currentBookId}/${currentChapterId}?t=${Date.now()}`;
                    audioPlayer.style.display = 'block';
                    audioPlayer.play();
                    document.getElementById('pauseBtn').disabled = false;
                    showStatus(result.cached ? '播放缓存音频' : '开始播放新合成音频', 'success');
                    
                    // 开始定期保存进度
                    startProgressSaving();
                    
                    // 刷新章节列表显示缓存状态
                    await loadChapters();
                } else {
                    showStatus(result.detail || '合成失败', 'error');
                }
            } catch (error) {
                showStatus('合成失败: ' + error.message, 'error');
            } finally {
                document.getElementById('playBtn').disabled = false;
            }
        }
        
        async function batchSynthesize() {
            if (!currentBookId) return;
            
            showStatus('开始批量合成...', 'info');
            
            try {
                const params = new URLSearchParams({
                    engine: currentEngine,
                    voice: currentVoice
                });
                
                const response = await fetch(
                    `/batch/synthesize/${currentBookId}?${params}`,
                    { method: 'POST' }
                );
                const result = await response.json();
                
                if (response.ok) {
                    showStatus(result.message, 'success');
                    // 开始定期检查状态
                    startStatusPolling();
                } else {
                    showStatus(result.detail || '批量合成失败', 'error');
                }
            } catch (error) {
                showStatus('批量合成失败: ' + error.message, 'error');
            }
        }
        
        let statusPollInterval = null;
        
        function startStatusPolling() {
            document.getElementById('batchStatus').style.display = 'block';
            checkBatchStatus();
            
            // 每2秒更新一次状态
            statusPollInterval = setInterval(async () => {
                const status = await checkBatchStatus();
                // 如果所有任务完成，停止轮询
                if (status && status.pending === 0 && status.processing === 0) {
                    clearInterval(statusPollInterval);
                    showStatus('批量合成完成！', 'success');
                    await loadChapters(); // 刷新章节列表
                }
            }, 2000);
        }
        
        async function checkBatchStatus() {
            try {
                const response = await fetch('/batch/status');
                const status = await response.json();
                
                document.getElementById('pendingCount').textContent = status.pending || 0;
                document.getElementById('processingCount').textContent = status.processing || 0;
                document.getElementById('completedCount').textContent = status.completed || 0;
                document.getElementById('failedCount').textContent = status.failed || 0;
                
                document.getElementById('batchStatus').style.display = 'block';
                
                return status;
            } catch (error) {
                console.error('获取状态失败:', error);
            }
        }
        
        function playPause() {
            const audio = document.getElementById('audioPlayer');
            if (audio.paused) {
                audio.play();
                document.getElementById('pauseBtn').textContent = '⏸️ 暂停';
            } else {
                audio.pause();
                document.getElementById('pauseBtn').textContent = '▶️ 播放';
            }
        }
        
        function updateRate(value) {
            const newRate = parseInt(value);
            // 如果语速变化较大（超过20），标记需要重新生成
            if (Math.abs(newRate - lastSynthesizedRate) > 20) {
                forceRegenerate = true;
                showStatus('语速已调整，下次播放将使用新语速', 'info');
            }
            currentRate = newRate;
            document.getElementById('rateValue').textContent = value;
        }
        
        // 加载播放进度
        async function loadProgress() {
            if (!currentBookId) return;
            
            try {
                const response = await fetch(`/progress/${currentBookId}`);
                if (response.ok) {
                    const progress = await response.json();
                    if (progress && progress.chapterId) {
                        lastSavedProgress = progress;
                        
                        // 显示上次播放信息
                        const progressInfo = document.getElementById('progressInfo');
                        const lastPosition = document.getElementById('lastPosition');
                        
                        if (progress.chapterTitle && progress.position > 0) {
                            const minutes = Math.floor(progress.position / 60);
                            const seconds = Math.floor(progress.position % 60);
                            lastPosition.textContent = `${progress.chapterTitle} - ${minutes}:${seconds.toString().padStart(2, '0')}`;
                            progressInfo.style.display = 'block';
                        }
                        
                        // 更新章节列表的书签标记
                        await loadChapters();
                    }
                }
            } catch (error) {
                console.error('加载进度失败:', error);
            }
        }
        
        // 保存播放进度
        async function saveProgress() {
            if (!currentBookId || !currentChapterId) return;
            
            const audioPlayer = document.getElementById('audioPlayer');
            if (!audioPlayer.src || audioPlayer.currentTime === 0) return;
            
            try {
                const params = new URLSearchParams({
                    book_id: currentBookId,
                    chapter_id: currentChapterId,
                    position: audioPlayer.currentTime
                });
                
                await fetch(`/progress/save?${params}`, { method: 'POST' });
                
                // 更新本地保存的进度
                lastSavedProgress = {
                    bookId: currentBookId,
                    chapterId: currentChapterId,
                    position: audioPlayer.currentTime
                };
            } catch (error) {
                console.error('保存进度失败:', error);
            }
        }
        
        // 开始定期保存进度
        function startProgressSaving() {
            // 清除之前的定时器
            if (saveProgressInterval) {
                clearInterval(saveProgressInterval);
            }
            
            // 每5秒保存一次进度
            saveProgressInterval = setInterval(saveProgress, 5000);
            
            // 音频结束时也保存进度
            const audioPlayer = document.getElementById('audioPlayer');
            audioPlayer.onended = () => {
                saveProgress();
                clearInterval(saveProgressInterval);
            };
            
            // 暂停时保存进度
            audioPlayer.onpause = () => {
                saveProgress();
            };
        }
        
        // 从书签位置继续播放
        async function resumeFromBookmark() {
            if (!lastSavedProgress || !lastSavedProgress.chapterId) return;
            
            // 选择上次的章节
            const chapterItem = document.querySelector(`[data-chapter-id="${lastSavedProgress.chapterId}"]`);
            if (chapterItem) {
                chapterItem.click();
                
                // 等待音频加载后跳转到保存的位置
                setTimeout(() => {
                    const audioPlayer = document.getElementById('audioPlayer');
                    if (audioPlayer.src) {
                        audioPlayer.currentTime = lastSavedProgress.position || 0;
                        audioPlayer.play();
                        showStatus(`已恢复到上次播放位置`, 'success');
                    }
                }, 1000);
            }
        }
        
        // 加载书籍列表
        async function loadBooks() {
            try {
                const response = await fetch('/books');
                const books = await response.json();
                
                if (books.length > 0) {
                    const bookManager = document.getElementById('bookManager');
                    bookManager.style.display = 'block';
                    
                    const bookList = document.getElementById('bookList');
                    bookList.innerHTML = books.map(book => `
                        <div class="book-card ${book.id === currentBookId ? 'active' : ''}" 
                             onclick="openBook(${book.id}, '${book.title.replace(/'/g, "\\'")}')"
                             data-book-id="${book.id}">
                            <div class="book-card-title" title="${book.title}">${book.title}</div>
                            <div class="book-card-info">章节数: ${book.chapterCount}</div>
                            <div class="book-card-info">已缓存: ${book.cachedCount}/${book.chapterCount}</div>
                            ${book.hasProgress ? '<div class="book-card-info">📍 有播放记录</div>' : ''}
                            <div class="book-card-actions">
                                <button class="btn-small btn-info" onclick="event.stopPropagation(); exportBook(${book.id})">导出</button>
                                <button class="btn-small btn-danger" onclick="event.stopPropagation(); deleteBook(${book.id})">删除</button>
                            </div>
                        </div>
                    `).join('');
                }
            } catch (error) {
                console.error('加载书籍列表失败:', error);
            }
        }
        
        // 打开书籍
        async function openBook(bookId, title) {
            currentBookId = bookId;
            document.getElementById('bookTitle').textContent = title;
            document.getElementById('bookInfo').style.display = 'block';
            document.getElementById('bookManager').style.display = 'none';
            
            // 更新书籍卡片状态
            document.querySelectorAll('.book-card').forEach(card => {
                if (card.dataset.bookId == bookId) {
                    card.classList.add('active');
                } else {
                    card.classList.remove('active');
                }
            });
            
            // 加载章节
            await loadChapters();
            document.getElementById('engineSettings').style.display = 'block';
            document.getElementById('mainInterface').style.display = 'grid';
            
            // 加载播放进度
            await loadProgress();
        }
        
        // 删除书籍
        async function deleteBook(bookId) {
            if (!confirm('确定要删除这本书吗？所有音频文件也将被删除。')) {
                return;
            }
            
            try {
                const response = await fetch(`/books/${bookId}`, { method: 'DELETE' });
                if (response.ok) {
                    showStatus('书籍已删除', 'success');
                    if (bookId === currentBookId) {
                        // 如果删除的是当前书籍，重置界面
                        currentBookId = null;
                        document.getElementById('bookInfo').style.display = 'none';
                        document.getElementById('engineSettings').style.display = 'none';
                        document.getElementById('mainInterface').style.display = 'none';
                    }
                    await loadBooks();
                } else {
                    showStatus('删除失败', 'error');
                }
            } catch (error) {
                showStatus('删除失败: ' + error.message, 'error');
            }
        }
        
        // 导出书籍音频
        async function exportBook(bookId) {
            const targetBookId = bookId || currentBookId;
            if (!targetBookId) {
                showStatus('请先选择一本书', 'error');
                return;
            }
            
            showStatus('正在准备导出...', 'info');
            
            try {
                // 创建下载链接
                const downloadUrl = `/export/${targetBookId}?format=mp3`;
                const link = document.createElement('a');
                link.href = downloadUrl;
                link.download = 'audiobook.zip';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                
                showStatus('音频导出成功！', 'success');
            } catch (error) {
                showStatus('导出失败: ' + error.message, 'error');
            }
        }
        
        // 显示书架
        function showBookList() {
            document.getElementById('bookManager').style.display = 'block';
            loadBooks();
        }
        
        // 拖拽上传
        document.addEventListener('dragover', e => e.preventDefault());
        document.addEventListener('drop', e => {
            e.preventDefault();
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                uploadFile(files[0]);
            }
        });
    </script>
</body>
</html>
        """
    
    def run(self, host="127.0.0.1", port=8080):
        """启动应用"""
        print(f"[START] 正在启动服务...")
        print(f"[URL] 访问地址: http://{host}:{port}")
        
        def open_browser():
            time.sleep(2)
            webbrowser.open(f"http://{host}:{port}")
        
        threading.Thread(target=open_browser, daemon=True).start()
        
        uvicorn.run(
            self.app,
            host=host,
            port=port,
            log_level="error",
            access_log=False
        )


def main():
    """主函数"""
    try:
        app = TTSEnhancedApp()
        app.run()
    except KeyboardInterrupt:
        print("\n[INFO] 程序已退出")
    except Exception as e:
        print(f"[ERROR] 启动失败: {e}")
        input("按回车键退出...")


if __name__ == "__main__":
    main()
