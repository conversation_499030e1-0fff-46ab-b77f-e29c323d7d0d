{"name": "tts-reader-frontend", "private": true, "version": "0.1.0", "description": "TXT→TTS 听书系统（MVP）前端：Electron + React + TypeScript + Vite", "main": "electron/main.ts", "type": "module", "scripts": {"dev:renderer": "vite", "build:renderer": "vite build", "preview": "vite preview", "dev:electron": "cross-env VITE_DEV_SERVER_URL=http://localhost:5173 node --loader ts-node/esm electron/main.ts", "dev": "npm-run-all -p dev:renderer dev:electron", "build": "vite build"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0"}, "devDependencies": {"@types/react": "^18.2.24", "@types/react-dom": "^18.2.11", "@vitejs/plugin-react": "^4.2.0", "typescript": "^5.3.3", "vite": "^5.0.0", "electron": "^28.0.0", "ts-node": "^10.9.2", "cross-env": "^7.0.3", "npm-run-all": "^4.1.5"}}