# Augment Agent 工作偏好设置

> 📅 创建时间：2025-06-16
> 🔄 最后更新：2025-07-27 星期日
> 🤖 适用对象：Augment Agent (基于 Claude Sonnet 4)
> 📝 版本：v4.1 (新增英中翻译标准化约定)
> 🎯 整合范围：核心发现、最佳实践、三层记忆协同机制、对话导出功能、翻译规则模板

---

## 🎯 基于MCP对比分析的核心发现

### 工具生态成熟度评估
基于《MCP工具与系统工具对比分析完整报告》的核心发现：

1. **工具选择优先级**：
   - 系统工具 > 官方MCP > 第三方MCP（稳定性和性能）
   - 简单工具 > 复杂工具（资源消耗和故障率）
   - 专业工具 > 通用工具（功能完整性）

2. **三层记忆管理架构**：
   - **Remember**：全局工作偏好和长期协作原则
   - **寸止MCP**：项目特定规则和临时上下文
   - **Memory MCP**：复杂知识图谱和关系网络

3. **五层架构工作模式**：
   - 用户交互层 → 智能决策层 → 专业工具层 → 记忆管理层 → 基础设施层

4. **智能工具组合策略**：
   - 日常开发：File Operations + Codebase Retrieval + Sequential Thinking
   - 复杂规划：Sequential Thinking + Shrimp Task Manager + mcp-feedback-enhanced
   - 技术调研：ACE + Web Search + Context7 + Fetch MCP + Sequential Thinking
   - 知识管理：Memory MCP + mcp-obsidian + 寸止MCP + Remember

---

## 🎯 核心工作流程

### 任务执行三阶段
1. **阶段1：任务分析与计划制定**
   - 复杂任务（预计超过3个主要步骤）自动创建任务计划文档
   - 任务命名格式：`核心功能-YYYYMMDD`
   - 计划文档存储：`./issues/任务名.md`

2. **阶段2：任务执行与反馈**
   - 严格按照 issues 文档中的计划逐步执行
   - 关键节点使用 `interactive-feedback` 工具反馈

3. **阶段3：任务复盘与总结**
   - 任务完成后创建复盘文档：`./rewind/任务名.md`
   - 包含问题分析、解决方案、经验总结、后续建议

### 深度分析机制
- **主动使用 sequential-thinking**：处理复杂任务时自动启用深度思考工具
- **透明化思考过程**：展示完整的推理链条和决策依据
- **多轮迭代优化**：通过连续思考提升分析质量和输出准确性

---

## 🛠️ 基于报告优化的MCP服务使用策略

### 核心服务（⭐⭐⭐⭐⭐ 稳定性）
- `Memory MCP`：知识图谱构建和跨会话记忆（官方，最高优先级）
- `Sequential Thinking`：复杂任务分析和深度思考（官方，逻辑推理首选）
- `Shrimp Task Manager`：项目任务规划和进度管理（第三方，功能完整）
- `mcp-feedback-enhanced`：关键节点用户反馈收集（第三方，交互丰富）
- `寸止MCP`：项目记忆管理和智能拦截（第三方，项目特定）

### 扩展服务（⭐⭐⭐⭐ 可用性）
- `Context7`：技术文档查询和最佳实践（官方，权威性高）
- `mcp-obsidian`：Obsidian知识库操作（第三方，功能专业）
- `Fetch MCP`：网页内容获取和解析（第三方，内容完整）

### 备用服务（⭐⭐⭐ 特定场景）
- `Playwright MCP`：浏览器自动化和网页截图（第三方，复杂交互）
- `Together Image Gen`：图像生成（第三方，备用方案）

### 智能工具选择算法
```python
def select_tool_by_scenario(scenario, complexity, performance_req):
    """基于报告分析的工具选择逻辑"""

    # 文件操作场景
    if scenario == "file_operation":
        return "File Operations"  # 系统工具绝对优先

    # 代码搜索场景
    elif scenario == "code_search":
        return "Codebase Retrieval"  # 语义搜索优势

    # 信息收集场景
    elif scenario == "information_gathering":
        if complexity == "simple":
            return "Web Search"
        elif complexity == "technical":
            return "Context7"
        else:
            return ["Web Search", "Context7", "Fetch MCP"]  # 组合使用

    # 任务管理场景
    elif scenario == "task_management":
        if complexity == "simple":
            return "系统工具"
        else:
            return ["Sequential Thinking", "Shrimp Task Manager"]

    # 用户交互场景
    elif scenario == "user_interaction":
        if complexity == "simple":
            return "系统交互"
        elif complexity == "complex":
            return "mcp-feedback-enhanced"
        else:
            return "寸止MCP"  # 智能拦截

    # 记忆管理场景
    elif scenario == "memory_management":
        if complexity == "global":
            return "Remember"
        elif complexity == "project":
            return "寸止MCP"
        else:
            return "Memory MCP"  # 知识图谱
```

### MCP工具协同原则（基于报告优化）
- **性能优先**：系统工具 > MCP工具，官方 > 第三方，简单 > 复杂
- **功能互补**：不同MCP工具负责不同功能领域，避免重复和冲突
- **分层管理**：三层记忆架构，分工明确，避免信息重复
- **故障切换**：MCP工具故障时自动切换到系统工具备用
- **测试验证**：新工具配置后必须进行功能测试和兼容性检查

---

## 🔧 技术配置标准流程

### MCP工具配置四阶段
1. **需求分析与信息收集**
   - 明确配置目标和预期功能
   - 使用 `web-fetch` 获取官方文档
   - 研究项目结构和配置方法
   - 查看相关Issue和已知问题

2. **配置实施与测试**
   - 按照官方文档进行配置
   - 逐步测试各项功能
   - 记录配置过程和参数
   - 验证与现有工具的兼容性

3. **问题排查与解决**
   - 遇到问题时查找相关Issue
   - 使用系统工具进行环境检查
   - 采用渐进式解决方案
   - 记录问题和解决方法

4. **文档化与复盘**
   - 创建详细的配置复盘文档
   - 提取可复用的工作原则
   - 更新工作偏好设置
   - 建立标准操作模板

### 问题解决标准机制
1. **问题识别**：准确描述问题现象和错误信息
2. **信息收集**：查看官方文档、Issue、错误日志
3. **方案制定**：基于信息制定多个可选解决方案
4. **渐进实施**：从简单到复杂逐步尝试解决方案
5. **效果验证**：测试解决方案的有效性和稳定性
6. **经验总结**：记录问题原因和解决方法供后续参考

### 环境依赖管理原则
- **环境检查优先**：配置前检查系统环境和依赖
- **渐进式解决**：从最简单的解决方案开始尝试
- **官方方案优先**：优先使用官方推荐的配置方法
- **兼容性验证**：确保新工具与现有环境兼容

---

## 🧠 知识重构原则

### 核心转化方向
- **模式识别 > 细节记忆**：提取规律和模式，而非堆砌细节
- **生动形象 > 抽象概念**：用具体、可视化的方式表达抽象内容
- **情感共鸣 > 理性说服**：触动情感，让知识有温度
- **连接已知 > 全新信息**：与现有知识体系建立联系
- **可行洞察 > 纯粹知识**：提供可执行的行动指导

### 转化目标
- **化繁为简**：不失深度地简化复杂内容
- **化虚为实**：不失准确性地具象化抽象概念
- **保持核心准确性**：在转化过程中维护内容的本质真实性

### 处理流程
1. **用户先提取文本**：用户负责内容提取
2. **AI 总结组织**：AI 负责总结和结构化组织
3. **先个别后整合**：每篇文章单独处理精华，再组合
4. **扩展整合**：在现有文章合集基础上添加新相关内容，重新整合

### Capture Notes 处理约定
- **搜索方式**：OR模式搜索 → 时间排序 → 创建专题文件
- **输出格式**：简化卡片式布局（工具名称 + 网址 + 简介 + 来源链接）
- **分类原则**：基于实际数据记录的分类系统，而非预设模板
- **处理流程**：去重分类 → 统计报告

---

## 📊 Obsidian 系统偏好

### 知识管理系统
- 问题导向的结构化框架
- 文档属性和标签体系
- 可视化仪表盘
- 定期回顾机制

### 精力管理系统
- 日记记录健康数据（精力、调理、看病）
- 支持关键词查询
- 可视化图表（柱状图、饼图）
- 点击跳转到原始位置

### 任务管理系统
- 简约三栏布局
- 进度指示器：🔄 和"进行中"
- 点击完成按钮
- 项目链接迁移机制

### 项目管理系统
- 单一状态属性：active/cancelled/completed/risk
- 彩色图标显示：🟢🟡🔴
- 进度显示：已用天数和剩余天数
- dataview TABLE 格式

### 番茄钟追踪
- 记录实际完成的番茄钟
- 日目标和达成率分析
- 月度报告页面

---

## 🛠️ 技术实现偏好

### 包管理器使用
- **严格使用包管理器**：npm、pip、cargo 等
- **禁止手动编辑**：package.json、requirements.txt 等配置文件
- **自动处理依赖**：版本解析、冲突处理、锁文件更新
- **原因说明**：避免版本冲突、依赖问题和构建失败

### 代码显示规范
```xml
<augment_code_snippet path="文件路径" mode="EXCERPT">
````语言
代码内容（不超过10行）
````
</augment_code_snippet>
```

### 权限控制约定
**⚠️ 严重警告**：以下操作需要明确用户许可才能执行：
- 提交或推送代码
- 更改工单状态
- 合并分支
- 安装依赖包
- 部署代码

### 困难恢复机制
- 发现陷入循环或重复调用相同工具时，主动向用户求助
- 不要在兔子洞里越陷越深
- 建议编写和运行测试来验证代码质量

### 图像生成偏好
- **格式**：高质量 JPG 格式
- **分辨率**：大尺寸，避免像素化
- **保存路径**：`C:\Users\<USER>\Desktop\测试库\cursor_projects\Ob`

---

## 💡 沟通协作偏好

### 基本原则
- **中文交流**：所有对话使用中文
- **诚实告知限制**：不能做的事情坦诚说明
- **提供替代方案**：给出可实现的解决方案
- **协作讨论**：重要决策前先讨论再实施

### 📅 日期验证强制要求 ⚠️ **严格执行**
- **每份文档必须验证日期**：创建任何文档前必须使用命令行确认当日准确日期
- **标准验证命令**：`Get-Date -Format 'yyyy-MM-dd dddd'` (PowerShell)
- **禁止假设猜测**：绝对不允许依赖记忆或预设的日期信息
- **标题内容一致**：文档标题和内容中的日期必须完全一致
- **历史文档保持**：已有历史文档的日期不随意更改

### 📋 日期标注标准格式
- **创建时间**：`YYYY-MM-DD 星期X`
- **更新时间**：`YYYY-MM-DD`
- **文件命名**：`项目名-YYYYMMDD.md`
- **示例**：`2025-07-03 星期四`

### 🔍 日期验证流程
1. **执行命令**：`Get-Date -Format 'yyyy-MM-dd dddd'`
2. **确认结果**：记录准确的日期和星期
3. **文档标注**：在文档头部添加准确时间信息
4. **一致性检查**：确保所有时间相关信息准确无误

### 界面偏好
- 保持当前界面显示设置
- 需要更改时用户会明确说明
- 优先视觉丰富的卡片式设计
- 紧凑布局，优化渲染速度

---

## 🚀 自动化与部署

### 脚本执行偏好
- 直接命令行执行 Python 脚本
- 创建 .bat 文件便于重复使用
- 一次性部署，可重复使用的解决方案

### 文本处理
- 结构化 Markdown 输出
- 保持原始含义，初期不添加 AI 解释
- 支持批量处理

### 英中翻译标准化约定
用户建立了标准化英中翻译规则模板：当用户说"翻译 `XXX文件`"时，以下规则自动应用：

#### 翻译规则模板
1. **翻译范围**：仅翻译英文文本内容，保持以下内容不变：
   - 代码片段（包括变量名、函数名、类名等）
   - 技术概念的专有名词（如API、JSON、HTTP等）
   - 文件路径和URL
   - 命令行指令
   - YAML前置元数据结构

2. **翻译质量**：确保翻译语言准确、自然流畅，符合中文表达习惯，保持原意的准确性

3. **格式保持**：完整保持原文档的Markdown格式结构（如标题层级、列表、代码块、表格等）

4. **输出要求**：
   - 将翻译后的完整内容保存为新文件
   - 文件名格式为 `原文件名_zh.md`
   - 使用Markdown格式输出
   - 保存在当前工作目录

5. **工作流程**：
   - 先读取原文件内容
   - 按照上述规则进行翻译
   - 保存为新的中文版本文件
   - 提供翻译完成确认

#### 自动化执行流程
当用户说"翻译 `文件名`"时，自动执行：
1. 📖 **读取原文件** - 获取完整内容
2. 🔄 **应用翻译规则** - 按照保存的5项规则进行翻译
3. 💾 **保存中文版本** - 创建 `原文件名_zh.md` 文件
4. ✅ **确认完成** - 提供翻译完成状态报告

---

## 📈 商业化考虑

### 模板销售
- 目标平台：小红书
- 价格区间：9.9元 - 79.9元
- 产品类型：Obsidian 管理系统模板
- 营销重点：治愈系奶茶风、Morandi 配色

---

## 🎨 推广图制作约定

### 标准指令格式
```
推广图：[内容来源] → [风格要求]
```

### 执行方式
- **工作目录**: `C:\Users\<USER>\Desktop\测试库\cursor_projects\Ob`
- **技术路线**: HTML网页设计 + 全页面截图生成JPG + 说明文档
- **截图参数**: 1400x2000视窗，full_page=True，quality=95
- **禁止使用**: `generate_image_together-image-gen` 工具

### 风格选项
1. **Bento风格** - 现代化网格布局，深色主题，适合技术内容
2. **冲击力风格** - 霓虹科技风，高对比度，适合产品发布
3. **简洁风格** - 清爽布局，重点突出，适合报告总结
4. **信息图风格** - 数据可视化，图表丰富，适合统计展示

### 使用示例
- `推广图：[[MCP配置指南]] → Bento风格`
- `推广图：番茄钟数据报告 → 信息图风格`
- `推广图：项目进展总结 → 简洁风格`
- `推广图：新功能发布 → 冲击力风格`

### AI响应流程
1. **内容分析** - 提取核心信息和关键数据
2. **设计规划** - 选择布局和视觉元素
3. **HTML创建** - 生成响应式网页设计
4. **JPG输出** - 自动截图并保存
5. **说明文档** - 提供设计说明和使用指南

### 默认规格
- **尺寸**: 1400x2000 (标准规格)
- **格式**: 高质量JPG
- **保存位置**: `cursor_projects/Ob/`
- **命名**: `[内容名称]推广图.jpg`

### 重要提醒
- 推广图生成在当前页面上下文中效果最佳
- 新页面中可能出现定位问题或显示不完整
- 建议在有规则/约定文档的页面中执行

---

## 🔄 持续优化

### 反馈机制
- 定期回顾工作偏好设置
- 根据使用情况调整优化
- 保持文档更新

### 版本管理
- 记录重要变更
- 保持向后兼容
- 明确升级路径

---

## 📁 路径约定

### 标准化路径
- **任务计划**: `./issues/任务名.md`
- **任务复盘**: `./rewind/任务名.md`
- **内容存储**: `./notes/`
- **推广图输出**: `./cursor_projects/Ob/`
- **图片导出**: `./cursor_projects/pic/images/`
- **文本转图**: `./cursor_projects/together/`
- **对话记录**: `./chat/` (包含四个子文件夹：important/projects/learning/daily)

---

## 💬 对话导出约定

### 三种导出指令定义
基于用户反馈和实际使用需求，建立了完整的对话导出机制：

#### 1. `导出对话` - 原始完整版
**功能**：导出完全原始的对话记录，按界面显示格式包含所有内容
**格式标准**：参考 `[2025-05-29_09-59-如何找回重置后的聊天记录.md]` 的完整格式
**包含内容**：
- 完整的User/Assistant对话
- 详细的思考过程（▼ Thought Process）
- 完整的工具调用过程（<function_calls>）
- 所有的参数和返回值（<function_results>）
- 从用户输入第一行开始的所有信息

**输出格式**：
```markdown
# 对话主题 (YYYY-MM-DD HH:MM:SS)

_**User**_

用户的完整输入

---

_**Assistant**_

▼ Thought Process
完整的思考过程内容

<function_calls>
<invoke name="工具名">
<parameter name="参数名">参数值</parameter>
</invoke>

```

<function_results>
工具调用结果
</function_results>

AI的详细回复内容

---

_**User**_

后续用户输入

---

_**Assistant**_

后续AI回复，包含所有思考过程和工具调用

---
```

#### 2. `导出对话：[主题关键词]` - 结构化整理版
**功能**：使用conversation-template.md模板生成结构化对话记录
**适用场景**：需要整理和总结的重要对话
**包含内容**：
- 对话概要和背景信息
- 关键问题和解决方案
- 代码修改记录
- 经验总结和后续行动

#### 3. `保存重要对话` - 深度分析版
**功能**：包含价值分析和经验总结的深度分析记录
**存储位置**：`chat/important/` 文件夹
**适用场景**：涉及重要技术决策、架构选择等关键对话
**包含内容**：
- 核心价值分析
- 深度经验总结
- 最佳实践提取
- 避免陷阱记录

### 智能分类规则
| 对话类型 | 存储位置 | 判断标准 |
|---------|---------|----------|
| **重要决策** | `chat/important/` | 涉及架构选择、技术方案决策 |
| **项目相关** | `chat/projects/` | 特定项目的功能开发、问题解决 |
| **学习探索** | `chat/learning/` | 新技术学习、工具研究 |
| **日常咨询** | `chat/daily/` | 一般性问题、简单咨询 |

### 自动执行流程
1. **日期验证** - 使用命令行确定当前准确日期
2. **智能分类** - 根据对话内容选择合适的子文件夹
3. **标准命名** - 生成格式：`YYYYMMDD_主题关键词.md`
4. **模板应用** - 使用对应的模板创建完整记录
5. **内容整理** - 包含对话内容、代码修改、决策总结等

### 文件命名规范
- **格式**：`YYYYMMDD_对话主题关键词.md`
- **示例**：`20250708_对话导出功能完整测试.md`
- **路径**：`./chat/[分类文件夹]/文件名.md`

---

## 🤝 三层记忆管理协同架构（基于报告优化）

### 三层记忆管理架构
基于《MCP工具与系统工具对比分析完整报告》的架构设计建议：

#### 全局记忆层 (Remember)
```yaml
存储范围: 跨项目的全局偏好和长期原则
数据类型: 工作习惯、协作原则、质量标准、技术偏好
更新频率: 低频更新，长期稳定（月度或季度）
访问模式: 全局读取，谨慎写入
典型内容:
- 编程语言偏好和代码风格
- 工作流程标准和质量要求
- 沟通协作原则和响应模式
- 技术选型标准和评估框架
```

#### 项目记忆层 (寸止MCP)
```yaml
存储范围: 项目特定的规则和临时上下文
数据类型: 项目约定、临时决策、阶段性规则、配置信息
更新频率: 中频更新，项目周期内有效（周度或双周）
访问模式: 项目内共享，灵活调整
典型内容:
- 项目特定的技术栈和工具配置
- 临时的工作约定和决策记录
- 阶段性的优先级和资源分配
- 项目相关的路径、命名规范
```

#### 知识图谱层 (Memory MCP)
```yaml
存储范围: 复杂知识关系和深度信息
数据类型: 实体关系、知识图谱、历史记录、学习成果
更新频率: 高频更新，持续积累（日度或实时）
访问模式: 智能检索，关联分析
典型内容:
- 技术概念和工具之间的关系网络
- 问题解决方案和经验积累
- 学习路径和知识依赖关系
- 项目历史和演进轨迹
```

### 智能路由机制
```python
def route_memory_operation(content_type, scope, complexity):
    """智能记忆路由算法"""

    # 全局性内容 → Remember
    if scope == "global" and complexity == "simple":
        return "Remember"

    # 项目特定内容 → 寸止MCP
    elif scope == "project" and complexity in ["simple", "medium"]:
        return "寸止MCP"

    # 复杂知识关系 → Memory MCP
    elif complexity == "complex" or content_type == "knowledge_graph":
        return "Memory MCP"

    # 默认选择
    else:
        return "Remember"
```

### 协同工作原则
- **分层存储**：避免信息重复，每层负责特定类型的信息
- **智能检索**：支持跨层查询，提供统一的检索接口
- **自动同步**：重要的项目经验自动提升为全局原则
- **冲突解决**：建立优先级机制，全局 > 项目 > 临时
- **定期维护**：定期清理过时信息，保持数据质量

### 寸止MCP特殊约定（基于实际使用经验）
- **git仓库要求**：寸止记忆管理功能需要在git仓库环境中使用
- **Windows路径问题**：注意Windows路径格式可能导致的识别问题
- **功能测试**：配置后必须测试对话拦截和记忆管理两个核心功能
- **与mcp-feedback-enhanced协同**：两个反馈工具功能互补，无冲突
- **项目路径环境变量**：确保CUNZHI_PROJECT_PATH正确设置

### 对话导出功能的三层记忆分工
基于实际测试和用户反馈，明确了对话导出功能在三层记忆系统中的分工：

#### Augment记忆（全局层）✅
**存储内容**：
- 触发指令机制：`导出对话`、`导出对话：[主题]`、`保存重要对话`
- 基本工作流程：日期验证→文件命名→模板应用→分类存储
- 全局标准：文件命名规范、模板结构、质量要求
- 适用范围：所有项目通用的对话导出规则

#### 寸止MCP（项目层）✅
**存储内容**：
- chat文件夹路径：`./chat/`
- 四个子文件夹：`important/projects/learning/daily`
- 模板文件：`conversation-template.md`和`quick-template.md`
- 智能分类规则：重要决策→important，项目相关→projects等
- 项目特定的导出配置和路径约定

#### Memory MCP（知识图谱层）⏸️
**计划存储内容**：
- 对话导出功能与其他工具的关系网络
- 用户工作流程的完整图谱和演进历史
- 历史使用记录和优化经验积累
- 对话主题分类的智能学习和优化建议

### 记忆系统协同原则
- **避免重复**：Augment记忆存储"是什么"和"怎么做"，寸止MCP存储"在哪里"和"当前如何配置"，Memory MCP存储"与什么相关"和"历史如何演进"
- **查询优先级**：日常使用→Augment记忆，项目配置→寸止MCP，深度分析→Memory MCP
- **自动同步**：重要的项目经验定期提升为全局原则
- **智能路由**：根据内容类型和复杂度自动选择合适的记忆层

---

*📝 备注：此文档为 Augment Agent 的核心工作指南，请在协作过程中严格遵循这些偏好设置。*

*🔄 更新日志：*
- *v4.1 (2025-07-27) - 新增英中翻译标准化约定，建立"翻译 `文件名`"触发机制，包含完整的翻译规则模板、质量标准、格式保持要求和自动化执行流程*
- *v4.0 (2025-07-08) - 新增对话导出约定和三层记忆系统分工，基于实际测试完善对话记录机制，明确三种导出指令的功能定义和使用场景*
- *v3.0 (2025-07-07) - 基于《MCP工具与系统工具对比分析完整报告》全面优化，整合核心发现、智能工具选择算法、三层记忆管理架构、最佳实践工作流程*
- *v2.1 - 新增MCP工具配置标准流程、问题解决机制、记忆管理协同约定、寸止MCP特殊约定*
- *v2.0 - 新增权限控制、困难恢复机制、Capture Notes处理约定、推广图制作详细规范、路径约定等内容*
