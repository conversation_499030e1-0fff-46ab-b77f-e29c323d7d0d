# -*- coding: utf-8 -*-
"""/cache/status 路由：缓存状态查询
- 列出指定书籍已缓存的章节（含文件大小），统计缓存体积与磁盘空间
- 不执行清理，仅给出建议提示字段
"""
from __future__ import annotations
from pathlib import Path
from typing import List, Optional
import shutil

from fastapi import APIRouter, HTTPException, Query
from pydantic import BaseModel
from sqlalchemy.orm import Session

from ..db.session import SessionLocal
from ..db.models import Book, Chapter

router = APIRouter()


class CachedChapter(BaseModel):
    chapterId: int
    idx: int
    path: str
    sizeBytes: int


class CacheStatus(BaseModel):
    bookId: int
    bookCacheDir: str
    cachedChapters: List[CachedChapter]
    totalCachedBytes: int
    diskTotal: Optional[int]
    diskUsed: Optional[int]
    diskFree: Optional[int]
    suggestion: Optional[str]


def get_db() -> Session:
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


def _book_cache_dir(book_id: int) -> Path:
    return Path("temp") / "cache" / f"book_{book_id}"


def _file_size(p: Path) -> int:
    try:
        return p.stat().st_size
    except Exception:
        return 0


@router.get("/cache/status", response_model=CacheStatus, tags=["cache"])
async def cache_status(
    bookId: int = Query(..., description="书籍ID"),
):
    db_gen = get_db()
    db = next(db_gen)
    try:
        book = db.query(Book).filter(Book.id == bookId).first()
        if not book:
            raise HTTPException(status_code=404, detail="书籍不存在")
        cache_dir = _book_cache_dir(bookId)
        entries: List[CachedChapter] = []
        total = 0
        q = db.query(Chapter).filter(Chapter.book_id == bookId, Chapter.cached_path.isnot(None))
        for ch in q.all():
            p = Path(ch.cached_path)
            size = _file_size(p)
            total += size
            entries.append(CachedChapter(chapterId=ch.id, idx=ch.idx, path=p.as_posix(), sizeBytes=size))

        # 磁盘空间（以缓存目录所在盘符为准）
        diskTotal = diskUsed = diskFree = None
        try:
            base = cache_dir if cache_dir.exists() else Path("temp")
            usage = shutil.disk_usage(base)
            diskTotal, diskUsed, diskFree = usage.total, usage.used, usage.free
        except Exception:
            pass

        suggestion = None
        # 简单建议：如果该书缓存超过 500MB 或磁盘剩余小于 1GB，建议清理
        if total > 500 * 1024 * 1024:
            suggestion = "缓存体积较大（>500MB），建议清理旧章节或调整质量设置。"
        if diskFree is not None and diskFree < 1 * 1024 * 1024 * 1024:
            suggestion = (suggestion + " 磁盘空间不足（<1GB）。") if suggestion else "磁盘空间不足（<1GB），建议清理缓存。"

        return CacheStatus(
            bookId=bookId,
            bookCacheDir=cache_dir.as_posix(),
            cachedChapters=entries,
            totalCachedBytes=total,
            diskTotal=diskTotal,
            diskUsed=diskUsed,
            diskFree=diskFree,
            suggestion=suggestion,
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"查询失败: {e}")
    finally:
        try:
            next(db_gen)
        except StopIteration:
            pass

