import React, { useEffect, useRef, useState } from 'react'
import ReactDOM from 'react-dom/client'

const API_BASE = (globalThis as any).env?.API_BASE || 'http://127.0.0.1:8000'

async function api<T>(path: string, init?: RequestInit): Promise<T> {
  const res = await fetch(`${API_BASE}${path}`, init)
  if (!res.ok) throw new Error(await res.text())
  return res.json()
}

async function loadTtsSettings(): Promise<{ rate?: number; engine?: 'local'|'edge' } | null> {
  try {
    const r = await api<{ items: Array<{ key: string; value?: any }> }>(`/settings/get?key=tts`)
    const it = r.items?.[0]
    return (it?.value || null) as any
  } catch { return null }
}

function App() {
  const [bookId, setBookId] = useState<number | null>(null)
  const [chapters, setChapters] = useState<any[]>([])
  const [currentChapterId, setCurrentChapterId] = useState<number | null>(null)
  const [bookmarks, setBookmarks] = useState<any[]>([])
  const audioRef = useRef<HTMLAudioElement | null>(null)
  const [playing, setPlaying] = useState(false)
  const [rate, setRate] = useState(1)  // UI播放速度，同时影响合成参数（pyttsx3: ~200*rate）
  const lastSavedSecRef = React.useRef(0)
  const [showSettings, setShowSettings] = useState(false)
  const [enginePriority, setEnginePriority] = useState<'local' | 'edge'>('local')
  const [isLoading, setIsLoading] = useState(false)
  const [toast, setToast] = useState<{ msg: string; type: 'info'|'error' } | null>(null)
  const showToast = (msg: string, type: 'info'|'error' = 'info') => { setToast({ msg, type }); setTimeout(() => setToast(null), 2500) }

  useEffect(() => { (async () => {
    const cfg = await loadTtsSettings()
    if (cfg?.rate && typeof cfg.rate === 'number') setRate(cfg.rate)
    if (cfg?.engine === 'edge' || cfg?.engine === 'local') setEnginePriority(cfg.engine)
  })() }, [])

  const canPlay = !!currentChapterId

  const handleImport = async () => {
    const p = prompt('输入TXT文件路径 (相对或绝对)：')
    if (!p) return
    const r = await api<{ bookId: number; meta: any }>(`/import?filePath=${encodeURIComponent(p)}`, { method: 'POST' })
    setBookId(r.bookId)
    alert(`导入成功：bookId=${r.bookId} 编码=${r.meta.encoding}`)
    setChapters([]); setCurrentChapterId(null); setBookmarks([])
  }

  const handlePreprocess = async () => {
    if (!bookId) return alert('请先导入TXT')
    const regex = prompt('章节正则 (留空走默认)：', '^(第.+?章|Chapter\\s+\\d+)') || ''
    const url = `/preprocess?bookId=${bookId}${regex ? `&chapterRegex=${encodeURIComponent(regex)}` : ''}`
    const r = await api<{ bookId: number; chapters: any[] }>(url, { method: 'POST' })
    setChapters(r.chapters)
    setCurrentChapterId(r.chapters?.[0]?.id ?? null)
    await reloadBookmarks()
    await tryResume()
  }

  const playCurrent = async (positionSec?: number) => {
    if (!bookId || !currentChapterId) return
    setIsLoading(true)
    try {
      const r = await api<{ audioPath: string }>(`/synthesize?bookId=${bookId}&chapterId=${currentChapterId}&rate=${Math.floor(200 * rate)}`, { method: 'POST' })
      const audio = audioRef.current!
      return new Promise<void>(async (resolve) => {
        const onLoaded = async () => {
          if (typeof positionSec === 'number') audio.currentTime = Math.max(positionSec, 0)
          await audio.play(); setPlaying(true); audio.removeEventListener('loadedmetadata', onLoaded); resolve(); setIsLoading(false)
        }
        audio.addEventListener('loadedmetadata', onLoaded)
        audio.src = r.audioPath
        audio.playbackRate = rate
      })
    } catch (e: any) {
      showToast(typeof e === 'string' ? e : (e?.message || '播放失败'), 'error')
      setIsLoading(false)
    }
  }

  const onTogglePlay = () => {
    const audio = audioRef.current!
    if (!audio.src) return
    if (audio.paused) { audio.play(); setPlaying(true) } else { audio.pause(); setPlaying(false) }
  }

  const seek = (delta: number) => {
    const audio = audioRef.current!
    if (!audio.src) return
    audio.currentTime = Math.max(audio.currentTime + delta, 0)
  }

  const saveProgress = async () => {
    if (!bookId || !currentChapterId) return
    const audio = audioRef.current!
    if (!audio.src) return
    const posMs = Math.floor(audio.currentTime * 1000)
    await api(`/progress/save?bookId=${bookId}&chapterId=${currentChapterId}&positionMs=${posMs}`, { method: 'POST' })
  }

  const onTimeUpdate = async () => {
    const audio = audioRef.current!
    const nowSec = Math.floor(audio.currentTime)
    if (nowSec - lastSavedSecRef.current >= 10) {
      lastSavedSecRef.current = nowSec
      try { await saveProgress() } catch {}
    }
  }

  const addBookmark = async () => {
    if (!bookId || !currentChapterId) return
    const name = prompt('书签名称（可空）：') || ''
    const posMs = Math.floor((audioRef.current?.currentTime || 0) * 1000)
    await api(`/bookmark/add?bookId=${bookId}&chapterId=${currentChapterId}&positionMs=${posMs}&name=${encodeURIComponent(name)}`, { method: 'POST' })
    await reloadBookmarks()
  }

  const reloadBookmarks = async () => {
    if (!bookId) return setBookmarks([])
    const r = await api<{ bookId: number; items: any[] }>(`/bookmark/list?bookId=${bookId}`)
    setBookmarks(r.items)
  }

  const jumpToBookmark = async (bm: any) => {
    if (!bookId) return
    setCurrentChapterId(bm.chapterId)
    await playCurrent((bm.positionMs || 0) / 1000)
  }

  const tryResume = async () => {
    if (!bookId) return
    try {
      const r = await api<{ found: boolean; data?: { bookId: number; chapterId: number; positionMs: number } }>(`/progress/last?bookId=${bookId}`)
      if (r.found && r.data?.chapterId) {
        if (confirm('检测到历史进度，是否恢复播放？')) {
          setCurrentChapterId(r.data.chapterId)
          await playCurrent((r.data.positionMs || 0) / 1000)
        }
      }
    } catch {}
  }

  return (
    <div style={{ fontFamily: 'system-ui, sans-serif', padding: 16 }} tabIndex={0} onKeyDown={async (e) => {
      if (e.ctrlKey && e.key === 'ArrowLeft') { e.preventDefault(); seek(-10) }
      if (e.ctrlKey && e.key === 'ArrowRight') { e.preventDefault(); seek(10) }
      if (e.ctrlKey && e.key === 'ArrowUp') { e.preventDefault(); setRate(v => Math.min(2, +(v + 0.1).toFixed(1))) }
      if (e.ctrlKey && e.key === 'ArrowDown') { e.preventDefault(); setRate(v => Math.max(0.5, +(v - 0.1).toFixed(1))) }
      if (e.key === ' ') { e.preventDefault(); onTogglePlay() }
      if (e.key === 'Enter') { e.preventDefault(); await playCurrent() }
    }}>

      <h2>TXT→TTS 听书（MVP）</h2>
      <div style={{ display: 'flex', gap: 12, marginBottom: 12, alignItems: 'center' }}>
        <button onClick={async () => {
          const arr = await (globalThis as any).electronAPI?.openFile?.()
          if (!arr || !arr.length) return
          const p = arr[0] as string
          const r = await api<{ bookId: number; meta: any }>(`/import?filePath=${encodeURIComponent(p)}`, { method: 'POST' })
          setBookId(r.bookId); setChapters([]); setCurrentChapterId(null); setBookmarks([])
        }}>选择TXT</button>
        <div onDragOver={(e) => { e.preventDefault() }} onDrop={async (e) => {
          e.preventDefault(); const f = e.dataTransfer.files?.[0]; if (!f) return
          const p = (f as any).path || f.name
          const r = await api<{ bookId: number; meta: any }>(`/import?filePath=${encodeURIComponent(p)}`, { method: 'POST' })
          setBookId(r.bookId); setChapters([]); setCurrentChapterId(null); setBookmarks([])
        }} style={{ border: '1px dashed #88c', padding: 6, cursor: 'copy' }}>或拖拽TXT到此</div>
        <button onClick={handlePreprocess} disabled={!bookId}>预处理分章</button>
        <button onClick={() => setShowSettings(true)} style={{ marginLeft: 'auto' }}>设置</button>
      </div>

      <div style={{ display: 'flex', gap: 16 }}>
        <div style={{ width: 300 }}>
          <h4>章节列表</h4>
          <ul>
            {chapters.map((c) => (
              <li key={c.id}>
                <button onClick={() => setCurrentChapterId(c.id)} style={{ fontWeight: currentChapterId === c.id ? 'bold' : 'normal' }}>
                  {c.idx}. {c.title || `第${c.idx}章`}
                </button>
              </li>
            ))}
          </ul>

          <h4>书签</h4>
          <div style={{ display: 'flex', gap: 8, marginBottom: 8 }}>
            <button onClick={addBookmark} disabled={!bookId || !currentChapterId}>添加书签</button>
            <button onClick={reloadBookmarks} disabled={!bookId}>刷新</button>
          </div>
          <ul>
            {bookmarks.map((b) => (
              <li key={b.id}>
                <button onClick={() => jumpToBookmark(b)}>
                  {(b.name || '书签')} · 章节#{b.chapterId ?? '-'} · {Math.floor((b.positionMs||0)/1000)}s
                </button>
              </li>
            ))}
          </ul>
        </div>

        <div style={{ flex: 1 }}>
          <h4>播放器</h4>
          <audio ref={audioRef} controls style={{ width: '100%' }} onEnded={() => setPlaying(false)} onTimeUpdate={onTimeUpdate} />
          {isLoading && <div style={{ marginTop: 8, color: '#666' }}>正在生成/加载音频…</div>}
          <div style={{ display: 'flex', alignItems: 'center', gap: 8, marginTop: 8 }}>
            <button onClick={() => seek(-10)}>-10s</button>
            <button onClick={onTogglePlay}>{playing ? '暂停' : '播放'}</button>
            <button onClick={() => seek(10)}>+10s</button>
            <label>速度: {rate.toFixed(2)}</label>
            <input type="range" min={0.5} max={2} step={0.1} value={rate} onChange={(e) => setRate(parseFloat(e.target.value))} />
            <button onClick={() => playCurrent()} disabled={!canPlay}>合成并播放当前章节</button>
            <button onClick={saveProgress} disabled={!bookId || !currentChapterId}>保存进度</button>
          </div>
        </div>
      </div>
      {showSettings && (
        <div style={{ position: 'fixed', inset: 0, background: 'rgba(0,0,0,0.35)', display: 'flex', alignItems: 'center', justifyContent: 'center' }} onClick={() => setShowSettings(false)}>
          <div style={{ background: '#fff', padding: 16, minWidth: 360 }} onClick={(e) => e.stopPropagation()}>
            <h4>设置</h4>
            <div style={{ display: 'grid', gap: 8 }}>
              <label>
                播放速度（0.5~2.0）
                <input type="range" min={0.5} max={2} step={0.1} value={rate} onChange={(e) => setRate(parseFloat(e.target.value))} />
                <span style={{ marginLeft: 8 }}>{rate.toFixed(2)}</span>
              </label>

              <label>
                引擎优先级
                <select value={enginePriority} onChange={(e) => setEnginePriority(e.target.value as any)}>
                  <option value="local">本地优先</option>
                  <option value="edge">云（edge）优先（预留）</option>
                </select>
              </label>

              <div style={{ display: 'flex', gap: 8, justifyContent: 'flex-end' }}>
                <button onClick={() => setShowSettings(false)}>取消</button>
                <button onClick={async () => {
                  // 保存到 /settings
                  const value = { rate, engine: enginePriority }
                  await api(`/settings/set?key=tts&valueJson=${encodeURIComponent(JSON.stringify(value))}`, { method: 'POST' })
                  setShowSettings(false)
                  alert('设置已保存')
                }}>保存</button>
              </div>
            </div>
          </div>
        </div>
      )}
      {toast && (
        <div style={{ position: 'fixed', bottom: 16, left: '50%', transform: 'translateX(-50%)', background: toast.type==='error'?'#fee':'#eef', color: '#333', padding: '8px 12px', borderRadius: 6 }}>
          {toast.msg}
        </div>
      )}
    </div>
  )
}

ReactDOM.createRoot(document.getElementById('root')!).render(<App />)

