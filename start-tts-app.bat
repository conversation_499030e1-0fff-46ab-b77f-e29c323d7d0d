@echo off
setlocal EnableDelayedExpansion

REM =========================
REM TXT→TTS 听书系统 一键启动脚本
REM 后端: FastAPI(Uvicorn) + 前端: Electron + Vite
REM 说明: 将分别在两个新窗口中启动后端与前端
REM =========================

REM 1) 解析项目根目录（本脚本所在目录）
set "ROOT=%~dp0"
cd /d "%ROOT%"

echo [INFO] Project root: %ROOT%

REM 2) 检查 Python
where python >nul 2>nul
if errorlevel 1 (
  echo [ERROR] 未检测到 Python。请安装 Python 3.10+ 后重试。
  pause
  exit /b 1
)

REM 3) 检查 Node/npm
where npm >nul 2>nul
if errorlevel 1 (
  echo [ERROR] 未检测到 npm。请安装 Node.js（包含 npm）后重试。
  pause
  exit /b 1
)

REM 4) 前端依赖（首次运行时自动安装）
if not exist "%ROOT%frontend\node_modules" (
  echo [INFO] 正在为前端安装依赖（首次运行）...
  pushd "%ROOT%frontend"
  call npm install
  if errorlevel 1 (
    echo [ERROR] npm install 失败，请检查网络或重试。
    popd
    pause
    exit /b 1
  )
  popd
)

REM 5) 提示 ffmpeg（可选）
where ffmpeg >nul 2>nul
if errorlevel 1 (
  echo [HINT] 未检测到 ffmpeg，将生成 WAV 音频。若需 MP3，请安装 ffmpeg 并加入 PATH。
)

REM 6) 启动后端（新窗口）
start "TTS Backend" cmd /k cd /d "%ROOT%" ^&^& python -m uvicorn backend.app.main:app --reload

REM 7) 启动前端（新窗口，包含 Vite + Electron）
start "TTS Frontend" cmd /k cd /d "%ROOT%frontend" ^&^& npm run dev

echo.
echo [OK] 已启动：
echo     - 后端窗口: TTS Backend
echo     - 前端窗口: TTS Frontend

echo [TIP] 若 Electron 未自动弹出，请查看 TTS Frontend 窗口日志。

echo.
pause
exit /b 0

