# 小说TXT转语音听书系统（MVP）实施计划（2025-08-08 星期五）

说明：以下任务列表仅包含“可由编码代理完成的代码类任务”，并按照增量构建顺序组织；每项引用 requirements.md 中的对应条目。

## 任务清单（可执行、增量、最多两级）

- [ ] 1. 初始化后端FastAPI骨架与SQLite接入（Req: 11.1, 12.1）
  - 建立FastAPI应用与基础路由结构（/health）
  - 集成SQLAlchemy/SQLite，创建books/chapters/progress/bookmarks/settings表模型
  - 完成数据库会话管理与迁移脚手架（简单版）

- [ ] 2. 实现TXT导入API /import（Req: 1.1~1.4, 9.1）
  - 参数：filePath
  - 功能：自动编码检测（UTF-8/UTF-8-BOM/GBK），失败返回可选编码提示；混合换行规范化；大文件分块读取
  - 入库：books记录（路径/编码/大小）
  - 返回：bookId, meta

- [ ] 3. 文本预处理API /preprocess（Req: 2.1~2.4）
  - 参数：bookId, rules{chapterRegex?, fallbackChunkSize?}
  - 功能：分章（正则→降级固定长度）、标点/空白规范化、特殊字符处理
  - 入库：chapters索引（title/offset/estDuration）

- [ ] 4. 合成队列与TTS接口 /synthesize（Req: 3.1~3.4, 4.1~4.4, 8.4, 11.2）
  - 任务队列：限制并发（1~2），失败重试与降级（local优先→cloud可选）
  - 本地SAPI5(pyttsx3)适配层：rate/pitch/pause参数映射
  - 生成MP3（单声道/22050~24000Hz/128kbps），返回audioPath

- [ ] 5. 缓存管理与索引 /cache/status（Req: 8.1~8.4, 9.1）
  - 章节MP3落地策略与索引维护（chapter→filePath）
  - 磁盘空间检查与清理提示（预留接口，先日志提示）

- [ ] 6. 进度与书签API（Req: 6.1~6.3, 12.1）
  - /progress/save, /progress/last, /bookmark/add, /bookmark/list, /bookmark/delete
  - 数据校验与容差处理（±2s）

- [ ] 7. 设置存取API（Req: 4.1~4.3, 3.2, 13.1）
  - /settings/get, /settings/set（rate/pitch/pause/engine）
  - 默认本地优先，云TTS需显式启用

- [ ] 8. 前端Electron+React项目初始化（Req: 7.1~7.4, 11.1）
  - Electron+Vite+React+TS脚手架；基本窗口与菜单
  - 全局主题（浅/深）与HiDPI适配

- [ ] 9. 前端播放器与控制条（Req: 5.1~5.4, 7.1, 7.4）
  - 组件：播放/暂停/进度条/速率滑条/章节列表侧栏
  - 播放器：HTMLAudio封装，seek(±5~15s)，章节跳转
  - 未缓存提示“生成/加载”并轮询状态

- [ ] 10. 导入与预处理前端流程（Req: 1.*, 2.*）
  - 文件选择器→调用/import→展示编码/大小→调用/preprocess→展示章节索引

- [ ] 11. 进度与书签前端流程（Req: 6.*）
  - 播放事件定时保存进度→启动恢复上次进度
  - 书签添加/列表/跳转/删除UI

- [ ] 12. 设置面板（Req: 4.*, 3.2, 13.*）
  - 语速/音调/停顿/引擎优先级切换；默认本地

- [ ] 13. 数据导入导出（JSON）（Req: 12.2）
  - 导出/导入设置与进度（前后端接口对接）

- [ ] 14. 错误与诊断模式（Req: 10.*）
  - 统一Toast与错误边界；诊断模式开关与日志级别

- [ ] 15. 非功能与性能守护（Req: 11.*）
  - 后台合成限速与并发限制；启动时间监控点

## 备注
- 执行顺序建议：后端1→2→3→4→5→6→7，再前端8→9→10→11→12→13→14→15；核心能力尽早打通（2→3→4→9→10）。
- 若在实施中发现需求/设计空缺，可回退到相应阶段修正后继续。

