# 📚 TXT→TTS 听书系统 (增强版)

> 🎯 一个功能强大的文本转语音听书系统，支持多种文档格式、多TTS引擎、批量合成等高级功能

## ✨ 新增功能亮点

### 🚀 核心增强
- **多TTS引擎支持**
  - 本地引擎：Windows SAPI5 (pyttsx3)
  - 云端引擎：Microsoft Edge TTS (edge-tts)
  - 智能切换和降级策略

- **批量预合成**
  - 异步任务队列管理
  - 多线程并发合成
  - 进度跟踪和状态监控
  - 可取消和重试机制

- **音频格式优化**
  - WAV高质量无损输出
  - MP3压缩格式支持
  - 自动格式转换
  - 可配置音频参数

- **高级配置管理**
  - 全局配置系统
  - 环境变量支持
  - 配置文件持久化
  - 运行时动态调整

## 📦 系统架构

```
TTS听书系统/
├── backend/                # 后端服务
│   ├── app/
│   │   ├── api/           # API路由
│   │   │   ├── batch_routes.py      # ⭐ 批量合成API
│   │   │   ├── synthesize_routes.py # ⭐ 增强的合成API
│   │   │   └── ...
│   │   ├── core/          # ⭐ 核心模块
│   │   │   └── config.py           # 配置管理
│   │   ├── services/      # 服务层
│   │   │   ├── tts_local.py        # 本地TTS
│   │   │   ├── tts_cloud.py        # ⭐ 云端TTS
│   │   │   ├── batch_synthesis.py  # ⭐ 批量合成
│   │   │   └── audio_encode.py     # 音频编码
│   │   └── db/           # 数据库
├── frontend/              # 前端界面
├── docs/                  # 文档
├── tts_app_standalone.py # 独立应用版本
└── requirements.txt       # ⭐ 完整依赖列表
```

## 🔧 安装部署

### 1. 环境要求
- Windows 10/11
- Python 3.8+
- 4GB+ RAM
- 10GB+ 可用磁盘空间

### 2. 安装依赖

```bash
# 克隆或下载项目
cd MVP版

# 创建虚拟环境（推荐）
python -m venv venv
venv\Scripts\activate  # Windows

# 安装依赖
pip install -r requirements.txt

# 安装可选的云端TTS
pip install edge-tts
```

### 3. 配置系统

创建 `config.json` 文件（可选）：

```json
{
  "DEFAULT_TTS_ENGINE": "cloud",
  "DEFAULT_VOICE": "zh-CN-XiaoxiaoNeural",
  "DEFAULT_RATE": 200,
  "AUDIO_FORMAT": "mp3",
  "ENABLE_CLOUD_TTS": true,
  "ENABLE_BATCH_SYNTHESIS": true,
  "MAX_CONCURRENT_SYNTHESIS": 3
}
```

### 4. 启动服务

#### 方式一：独立应用（推荐）
```bash
python tts_app_standalone.py
```

#### 方式二：分离启动
```bash
# 启动后端API
cd backend
uvicorn app.main:app --reload --port 8000

# 启动前端（另开终端）
cd frontend
npm install
npm run dev
```

## 📖 使用指南

### 基础使用流程

1. **导入文档** - 支持 TXT/DOCX/PDF/EPUB
2. **预处理分章** - 智能识别或自定义规则
3. **选择TTS引擎** - 本地或云端
4. **合成播放** - 单章或批量
5. **享受听书** - 随时暂停续播

### API 接口说明

#### 🆕 批量合成
```http
POST /batch/synthesize/{book_id}
{
  "chapter_ids": [1, 2, 3],  // 可选，不指定则全部
  "engine": "cloud",          // local 或 cloud
  "voice": "zh-CN-XiaoxiaoNeural",
  "rate": 200
}
```

#### 🆕 云端TTS合成
```http
POST /synthesize/cloud
{
  "bookId": 1,
  "chapterId": 1,
  "voice": "zh-CN-XiaoxiaoNeural",
  "rate": "+25%",
  "volume": "+0%"
}
```

#### 🆕 获取可用语音
```http
GET /voices
```

#### 🆕 批量任务状态
```http
GET /batch/status
GET /batch/task/{task_id}
```

### 云端语音选项

| 语音名称 | 性别 | 风格 | 推荐场景 |
|---------|------|------|---------|
| 晓晓 XiaoxiaoNeural | 女 | 活泼 | 轻松小说 |
| 云希 YunxiNeural | 男 | 标准 | 通用朗读 |
| 晓伊 XiaoyiNeural | 女 | 温柔 | 言情小说 |
| 云扬 YunyangNeural | 男 | 新闻 | 纪实文学 |
| 晓萱 XiaoxuanNeural | 女 | 成熟 | 职场文学 |
| 云健 YunjianNeural | 男 | 体育 | 竞技小说 |

## 🎯 高级功能

### 批量预合成
```python
# 使用API批量合成整本书
import requests

response = requests.post(
    "http://localhost:8000/batch/synthesize/1",
    params={
        "engine": "cloud",
        "voice": "zh-CN-XiaoxiaoNeural"
    }
)
```

### 配置管理
```python
# 程序内动态调整配置
from backend.app.core.config import settings

settings.DEFAULT_TTS_ENGINE = "cloud"
settings.ENABLE_BATCH_SYNTHESIS = True
settings.save_config()  # 保存到文件
```

### 缓存管理
```python
# 自动清理旧缓存
from backend.app.core.config import settings

deleted = settings.clean_old_cache(max_size_gb=5.0)
print(f"清理了 {deleted} 个缓存文件")
```

## 🚀 性能优化

### 推荐配置
- **批量合成**: 2-3个并发线程
- **音频格式**: MP3 128kbps（平衡质量和大小）
- **缓存策略**: 10GB上限，LRU清理
- **云端TTS**: 网络良好时优先使用

### 性能指标
- 本地TTS: ~100字/秒
- 云端TTS: ~200字/秒（取决于网络）
- MP3压缩: 减少80%文件大小
- 批量合成: 3倍速度提升

## 🔍 故障排除

### 常见问题

**Q: 云端TTS无法使用？**
```bash
pip install edge-tts
# 检查网络连接
```

**Q: 批量合成失败？**
- 检查磁盘空间
- 查看任务状态API
- 检查日志文件

**Q: 音频文件过大？**
- 启用MP3压缩
- 调整音频参数
- 定期清理缓存

## 📝 开发计划

### 近期优化
- [ ] 添加更多TTS引擎（百度、讯飞）
- [ ] 支持断点续传
- [ ] 添加语音识别矫正
- [ ] 实现章节智能合并

### 长期规划
- [ ] 多语言支持
- [ ] 移动端应用
- [ ] 云端同步
- [ ] AI语音克隆

## 📄 许可证

MIT License - 自由使用、修改和分发

## 🤝 贡献指南

欢迎提交Issue和Pull Request！

### 开发环境设置
```bash
# 安装开发依赖
pip install -r requirements.txt

# 运行测试
pytest tests/

# 代码格式化
black backend/
```

## 📞 联系支持

- 提交Issue报告问题
- 查看Wiki获取详细文档
- 加入讨论组交流使用心得

---

**💡 提示**: 首次使用建议先尝试本地TTS，确保系统正常后再配置云端服务。

**⚡ 快速开始**: 直接运行 `python tts_app_standalone.py` 即可体验完整功能！
