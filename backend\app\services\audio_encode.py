# -*- coding: utf-8 -*-
"""音频编码工具
- 将WAV转MP3（使用pydub+ffmpeg，若不可用则降级仅输出WAV）
"""
from __future__ import annotations
from pathlib import Path


def wav_to_mp3(wav_path: Path, mp3_path: Path, bitrate: str = "128k", sample_rate: int = 24000) -> bool:
    """尝试将WAV转MP3。
    返回 True 表示成功，False 表示失败（例如未安装pydub/ffmpeg）。
    """
    try:
        from pydub import AudioSegment  # type: ignore
    except Exception:
        return False

    try:
        audio = AudioSegment.from_wav(wav_path.as_posix())
        audio = audio.set_frame_rate(sample_rate).set_channels(1)
        mp3_path.parent.mkdir(parents=True, exist_ok=True)
        audio.export(mp3_path.as_posix(), format="mp3", bitrate=bitrate)
        return True
    except Exception:
        return False

