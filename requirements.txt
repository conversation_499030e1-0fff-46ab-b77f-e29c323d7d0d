# Core Web Framework
fastapi==0.104.1
uvicorn[standard]==0.24.0
python-multipart==0.0.6

# Database
sqlalchemy==2.0.23
alembic==1.12.1

# TTS Engines
pyttsx3==2.90  # Local TTS (Windows SAPI5)
edge-tts==6.1.9  # Cloud TTS (Microsoft Edge)

# Audio Processing
pydub==0.25.1  # Audio format conversion
ffmpeg-python==0.2.0  # FFmpeg wrapper (optional)

# Document Processing
python-docx==1.1.0  # DOCX support
PyPDF2==3.0.1  # PDF support
ebooklib==0.18  # EPUB support
beautifulsoup4==4.12.2  # HTML parsing for EPUB
lxml==4.9.3  # XML processing

# Text Processing
chardet==5.2.0  # Encoding detection
regex==2023.10.3  # Advanced regex support

# Async Support
aiofiles==23.2.1
asyncio==3.4.3

# Utilities
python-dotenv==1.0.0  # Environment variables
pydantic==2.5.0  # Data validation
pydantic-settings==2.1.0  # Settings management

# Development Tools (optional)
pytest==7.4.3
pytest-asyncio==0.21.1
black==23.11.0
flake8==6.1.0
mypy==1.7.0

# Packaging (for standalone exe)
pyinstaller==6.2.0

# Windows specific (optional)
pywin32==306  # Windows API access
comtypes==1.2.0  # COM interface support
