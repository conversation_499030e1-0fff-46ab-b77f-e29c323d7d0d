# TTSReader 打包与分发最佳实践（总结）

更新时间：2025-08-09 13:21:28 +08:00

本文档记录了在 Windows 环境下将本项目打包为单文件 EXE，并生成/更新分发 ZIP 的稳定做法与注意事项，供后续复用。

---

## 1. 构建目标与产物
- EXE：`dist/TTSReader.exe`
- 标准分发包：`dist/TTSReader_Windows_x64.zip`
- 备份分发包（可选）：`dist/TTSReader_Windows_x64_YYYYMMDD.zip`

## 2. 强制“干净重建”流程（推荐）
1. 清理旧构建与产物：
   - 删除 `build/` 目录
   - 删除 `dist/TTSReader.exe`
2. 使用 spec 文件构建（更可控）：
   - 命令：`pyinstaller --noconfirm --clean TTSReader.spec`
3. 构建完成后用 PowerShell 验证：
   - `Get-ChildItem dist | Select Name,Length,LastWriteTime`

## 3. 分发 ZIP 的稳定打包策略
- 避免复杂的一行式 `powershell -Command`，统一使用脚本文件：
  - 生成带日期 ZIP：`build_zip.ps1`
  - 覆盖标准 ZIP：`overwrite_zip.ps1`
- 打包时先把需要的文件拷贝到临时目录，再整体压缩（避免路径/转义/中文兼容问题）：
  - 必选：`dist/TTSReader.exe`
  - 可选：`dist/快速开始-三步说明.txt`、`README.md`、`LICENSE`
- 压缩完成后清理临时目录，删除失败时重试几次以规避文件被占用。

## 4. 命名与校验
- 标准包：`dist/TTSReader_Windows_x64.zip`
- 备份包：`dist/TTSReader_Windows_x64_YYYYMMDD.zip`
- 校验命令：`Get-ChildItem dist | Select Name,Length,LastWriteTime | Format-Table -AutoSize`

## 5. 常见问题
- 资源管理器不刷新：按 F5 或重开窗口。
- 一行式命令报转义/缺参错误：改用脚本文件。
- 压缩包含中文名失败：先复制到临时目录后再压缩。
- 打包后时间戳没有变化：先清理 build/ 与旧 dist/TTSReader.exe 后再 `--clean` 构建。

## 6. 脚本位置
- `build_zip.ps1`：生成 `dist/TTSReader_Windows_x64_YYYYMMDD.zip`
- `overwrite_zip.ps1`：覆盖 `dist/TTSReader_Windows_x64.zip`

---

以上为经过验证的稳定流程，可直接复用。待仓库初始化 git 后，建议将本最佳实践同步到“寸止记忆”。



---

## 附：关键经验与最佳实践摘要（可用于寸止记忆）

1) 打包依赖管理经验：PyInstaller 构建缺功能时，先安装依赖（如 PyPDF2/python-docx/ebooklib/bs4/lxml），在 .spec 的 hiddenimports 显式声明，最后 `--clean` 干净重建。

2) 前端错误处理最佳实践：上传入口的 fetch 加完整错误处理（JSON 解析失败的降级、HTTP 状态码检查、用户友好报错），避免“操作中断无提示”。

3) PowerShell 脚本化打包策略：不要使用复杂一行式命令；使用独立 .ps1，先复制到临时目录再整体压缩，并对 Remove/Compress 加重试避免文件被占用。

4) 构建验证与时间戳确认：每次重打包后，用 `Get-ChildItem dist | Select Name,Length,LastWriteTime` 核对文件大小与修改时间，确认构建已更新。
