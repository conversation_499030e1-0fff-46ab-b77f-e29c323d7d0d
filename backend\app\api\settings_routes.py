# -*- coding: utf-8 -*-
"""设置存取 API
- /settings/get?key? 获取单项或所有设置
- /settings/set?key&valueJson 设置某项（valueJson需是JSON字符串）
用例：存储 rate/pitch/pause/engine 等播放器与TTS设置。
"""
from __future__ import annotations
import json
from typing import Optional, List

from fastapi import APIRouter, HTTPException, Query
from pydantic import BaseModel
from sqlalchemy.orm import Session

from ..db.session import SessionLocal
from ..db.models import Setting

router = APIRouter()


class SettingItem(BaseModel):
    key: str
    value: Optional[dict]


class SettingsList(BaseModel):
    items: List[SettingItem]


def get_db() -> Session:
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


@router.get("/settings/get", response_model=SettingsList, tags=["settings"])
async def settings_get(
    key: Optional[str] = Query(None, description="可选，指定某个key，仅返回该项"),
):
    db_gen = get_db()
    db = next(db_gen)
    try:
        items: List[SettingItem] = []
        if key:
            rec = db.query(Setting).filter(Setting.key == key).first()
            if rec:
                try:
                    val = json.loads(rec.value_json) if rec.value_json else None
                except Exception:
                    val = None
                items.append(SettingItem(key=rec.key, value=val))
        else:
            for rec in db.query(Setting).all():
                try:
                    val = json.loads(rec.value_json) if rec.value_json else None
                except Exception:
                    val = None
                items.append(SettingItem(key=rec.key, value=val))
        return SettingsList(items=items)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取设置失败: {e}")
    finally:
        try:
            next(db_gen)
        except StopIteration:
            pass


@router.post("/settings/set", response_model=SettingItem, tags=["settings"])
async def settings_set(
    key: str = Query(..., description="设置键"),
    valueJson: str = Query(..., description="JSON字符串，如 {\"rate\":180}")
):
    # 简单验证valueJson为JSON
    try:
        parsed = json.loads(valueJson)
        if not isinstance(parsed, (dict, list, int, float, str, bool)):
            raise ValueError("不支持的JSON类型")
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"valueJson需为合法JSON: {e}")

    db_gen = get_db()
    db = next(db_gen)
    try:
        rec = db.query(Setting).filter(Setting.key == key).first()
        if rec:
            rec.value_json = valueJson
        else:
            rec = Setting(key=key, value_json=valueJson)
            db.add(rec)
        db.commit()
        return SettingItem(key=key, value=parsed if isinstance(parsed, dict) else {"value": parsed})
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"保存设置失败: {e}")
    finally:
        try:
            next(db_gen)
        except StopIteration:
            pass

