# TXT→TTS 听书系统 - 桌面应用版

> 📅 创建时间：2025-08-08 星期四  
> 🎯 版本：v1.0 独立桌面应用  
> 📦 文件大小：26.1 MB  
> 🖥️ 支持系统：Windows 10/11

## 🚀 快速开始

### 启动应用
1. **双击启动**：`dist\TTS听书系统.exe`
2. **自动打开浏览器**：应用会自动在默认浏览器中打开界面
3. **首次启动**：可能需要3-5秒加载时间

### 基本使用流程
```
选择TXT文件 → 预处理分章 → 选择章节 → 合成播放 → 享受听书
```

## 📖 详细操作指南

### 1. 导入TXT文件
- **方式一**：点击"📁 点击选择TXT文件"按钮
- **方式二**：直接拖拽TXT文件到上传区域
- **支持格式**：`.txt` 文本文件
- **编码支持**：UTF-8、GBK、GB2312、UTF-16（自动检测）
- **文件路径**：支持中文路径和空格

### 2. 预处理分章
- **自动分章**：点击"🔄 预处理分章"使用默认规则
- **自定义正则**：输入章节正则表达式，如：
  - `^(第[一二三四五六七八九十百千万0-9]+章.*?)$` - 匹配中文数字章节
  - `^(Chapter \d+.*?)$` - 匹配英文章节
  - `^(\d+\..*?)$` - 匹配数字编号章节
- **降级处理**：正则匹配失败时自动按固定长度分割

### 3. 章节播放
- **选择章节**：点击左侧章节列表中的任意章节
- **合成播放**：点击"🎤 合成并播放"按钮
- **播放控制**：
  - ⏸️ 暂停/▶️ 播放
  - 进度条拖拽
  - 音量调节
- **语速调节**：使用滑块调整语速（100-300）

## ⚙️ 功能特性

### 🎵 音频合成
- **TTS引擎**：Windows SAPI5（系统内置）
- **音频格式**：WAV（高质量无损）
- **语速范围**：100-300（可调节）
- **缓存机制**：合成后自动缓存，避免重复合成

### 📚 文本处理
- **智能分章**：支持正则表达式自定义分章规则
- **编码检测**：自动检测文件编码，支持多种中文编码
- **文本清理**：自动处理特殊字符和格式

### 💾 数据管理
- **内存数据库**：使用SQLite内存数据库，关闭应用后数据清空
- **音频缓存**：临时目录缓存音频文件，提高播放效率
- **进度记录**：支持播放进度记录（当前版本为基础实现）

## 🔧 技术架构

### 核心组件
- **后端**：FastAPI + SQLite + pyttsx3
- **前端**：内置HTML5界面（响应式设计）
- **打包**：PyInstaller单文件打包
- **音频**：Windows SAPI5 TTS引擎

### 文件结构
```
TTS听书系统.exe          # 主程序（26.1MB）
├── 内置Web服务器         # FastAPI后端
├── 内置浏览器界面        # HTML5前端
├── TTS引擎              # pyttsx3 + SAPI5
└── 数据库               # SQLite内存数据库
```

## 🎯 使用场景

### 适用人群
- 📖 **小说爱好者**：将网络小说转换为有声书
- 👁️ **视力保护**：减少长时间阅读对眼睛的负担
- 🚗 **通勤听书**：开车、坐车时听书
- 🏃 **运动听书**：跑步、健身时听书
- 👴 **老年用户**：操作简单，界面清晰

### 支持内容
- **网络小说**：起点、晋江等平台下载的TXT
- **电子书籍**：转换为TXT格式的各类书籍
- **学习资料**：课件、文档等文本内容
- **新闻文章**：保存为TXT的新闻、博客文章

## ⚠️ 注意事项

### 系统要求
- **操作系统**：Windows 10/11
- **内存**：建议4GB以上
- **磁盘空间**：100MB临时缓存空间
- **网络**：首次启动需要打开浏览器（本地服务）

### 使用限制
- **文件大小**：建议单个TXT文件不超过50MB
- **章节数量**：建议不超过1000章（性能考虑）
- **同时播放**：仅支持单章节播放
- **音频格式**：仅输出WAV格式

### 故障排除
- **启动失败**：检查是否被杀毒软件拦截
- **浏览器未打开**：手动访问 http://127.0.0.1:8080
- **音频无声**：检查系统音量和TTS服务
- **分章异常**：尝试使用自定义正则表达式

## 🔄 版本信息

### 当前版本：v1.0
- ✅ 基础TXT导入和分章
- ✅ SAPI5 TTS合成
- ✅ Web界面播放控制
- ✅ 音频缓存机制
- ✅ 语速调节功能

### 后续计划
- 🔲 多TTS引擎支持（edge-tts云端）
- 🔲 MP3格式输出
- 🔲 播放进度保存
- 🔲 书签功能
- 🔲 批量预合成
- 🔲 界面主题切换

## 📞 技术支持

### 常见问题
1. **Q：为什么只有WAV没有MP3？**  
   A：当前版本专注稳定性，使用WAV确保兼容性。后续版本将支持MP3。

2. **Q：可以更换TTS语音吗？**  
   A：当前使用系统默认语音，可在Windows设置中更改系统TTS语音。

3. **Q：支持其他格式的电子书吗？**  
   A：当前仅支持TXT，其他格式请先转换为TXT。

4. **Q：音频文件保存在哪里？**  
   A：临时目录自动管理，关闭应用后自动清理。

### 反馈渠道
- 使用过程中遇到问题，请保存错误信息
- 功能建议和改进意见欢迎反馈

---

**🎉 享受您的听书时光！**

*本应用为独立桌面版本，无需安装Python、Node.js等开发环境，双击即用。*
