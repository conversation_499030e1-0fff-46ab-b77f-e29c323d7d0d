#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TTS听书系统 - EXE打包脚本
使用PyInstaller将应用打包为单个可执行文件
"""
import os
import sys
import subprocess
from pathlib import Path

def install_requirements():
    """安装必要依赖"""
    requirements = [
        "fastapi",
        "uvicorn[standard]",
        "pyttsx3",
        "pydub",
        "pyinstaller"
    ]
    
    print("📦 安装依赖包...")
    for req in requirements:
        try:
            subprocess.run([sys.executable, "-m", "pip", "install", req], 
                         check=True, capture_output=True)
            print(f"✅ {req}")
        except subprocess.CalledProcessError as e:
            print(f"❌ {req} 安装失败: {e}")
            return False
    return True

def build_exe():
    """构建EXE文件"""
    print("🔨 开始构建EXE...")
    
    # PyInstaller命令
    cmd = [
        sys.executable, "-m", "PyInstaller",
        "--onefile",                    # 单文件
        "--noconsole",                  # 无控制台窗口
        "--name=TTS听书系统",           # 程序名称
        "--hidden-import=pyttsx3.drivers",
        "--hidden-import=pyttsx3.drivers.sapi5",
        "--hidden-import=uvicorn.lifespan.on",
        "--hidden-import=uvicorn.lifespan.off",
        "--hidden-import=uvicorn.protocols.websockets.auto",
        "--hidden-import=uvicorn.protocols.http.auto",
        "--hidden-import=uvicorn.protocols.http.h11_impl",
        "--hidden-import=uvicorn.protocols.websockets.wsproto_impl",
        "--hidden-import=uvicorn.loops.auto",
        "--hidden-import=uvicorn.logging",
        "--collect-all=pyttsx3",
        "--exclude-module=tkinter",     # 排除不需要的模块
        "--exclude-module=matplotlib",
        "--exclude-module=PIL",
        "tts_app_standalone.py"
    ]
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✅ EXE构建成功！")
        
        # 查找生成的EXE文件
        dist_dir = Path("dist")
        exe_files = list(dist_dir.glob("*.exe"))
        if exe_files:
            exe_path = exe_files[0]
            print(f"📁 EXE文件位置: {exe_path.absolute()}")
            print(f"📊 文件大小: {exe_path.stat().st_size / 1024 / 1024:.1f} MB")
            return True
        else:
            print("❌ 未找到生成的EXE文件")
            return False
            
    except subprocess.CalledProcessError as e:
        print(f"❌ 构建失败: {e}")
        print("错误输出:", e.stderr)
        return False

def create_spec_file():
    """创建PyInstaller spec文件（高级配置）"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['tts_app_standalone.py'],
    pathex=[],
    binaries=[],
    datas=[],
    hiddenimports=[
        'pyttsx3.drivers',
        'pyttsx3.drivers.sapi5',
        'uvicorn.lifespan.on',
        'uvicorn.lifespan.off',
        'uvicorn.protocols.websockets.auto',
        'uvicorn.protocols.http.auto',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='TTS听书系统',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
'''
    
    with open("tts_app.spec", "w", encoding="utf-8") as f:
        f.write(spec_content)
    print("📝 已创建 tts_app.spec 配置文件")

def main():
    """主构建流程"""
    print("🎯 TTS听书系统 - EXE打包工具")
    print("=" * 50)
    
    # 检查主程序文件
    if not Path("tts_app_standalone.py").exists():
        print("❌ 未找到 tts_app_standalone.py 文件")
        return False
    
    # 安装依赖
    if not install_requirements():
        print("❌ 依赖安装失败")
        return False
    
    # 创建spec文件
    create_spec_file()
    
    # 构建EXE
    if build_exe():
        print("\n🎉 构建完成！")
        print("📋 使用说明:")
        print("   1. 双击 dist/TTS听书系统.exe 启动应用")
        print("   2. 自动打开浏览器界面")
        print("   3. 选择TXT文件开始听书")
        print("\n💡 提示:")
        print("   - 首次运行可能需要几秒钟启动")
        print("   - 支持拖拽TXT文件到界面")
        print("   - 音频文件缓存在临时目录")
        return True
    else:
        print("❌ 构建失败")
        return False

if __name__ == "__main__":
    success = main()
    input(f"\n{'构建完成' if success else '构建失败'}，按回车键退出...")
