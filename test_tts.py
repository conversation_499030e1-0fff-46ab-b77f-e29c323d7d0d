#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TTS测试脚本 - 诊断语音合成问题
"""
import pyttsx3
import tempfile
from pathlib import Path
import time

def test_tts():
    """测试TTS功能"""
    print("🔊 开始TTS测试...")
    
    try:
        # 初始化TTS引擎
        print("1. 初始化TTS引擎...")
        engine = pyttsx3.init()
        
        # 获取可用的语音
        voices = engine.getProperty('voices')
        print(f"2. 可用语音数量: {len(voices) if voices else 0}")
        
        if voices:
            for i, voice in enumerate(voices):
                print(f"   语音{i}: {voice.name} ({voice.languages})")
        
        # 设置语音属性
        print("3. 设置语音属性...")
        engine.setProperty('rate', 200)  # 语速
        engine.setProperty('volume', 1.0)  # 音量
        
        # 测试文本
        test_text = "你好，这是TTS测试。Hello, this is a TTS test."
        print(f"4. 测试文本: {test_text}")
        
        # 方法1：直接播放
        print("5. 测试直接播放...")
        try:
            engine.say(test_text)
            engine.runAndWait()
            print("   ✅ 直接播放成功")
        except Exception as e:
            print(f"   ❌ 直接播放失败: {e}")
        
        # 方法2：保存到文件
        print("6. 测试保存到文件...")
        temp_dir = Path(tempfile.gettempdir()) / "tts_test"
        temp_dir.mkdir(exist_ok=True)
        audio_file = temp_dir / "test.wav"
        
        try:
            engine.save_to_file(test_text, str(audio_file))
            engine.runAndWait()
            
            if audio_file.exists():
                file_size = audio_file.stat().st_size
                print(f"   ✅ 文件保存成功: {audio_file} ({file_size} bytes)")
                
                # 检查文件是否有内容
                if file_size > 1000:  # 至少1KB
                    print("   ✅ 音频文件大小正常")
                else:
                    print("   ⚠️ 音频文件太小，可能有问题")
            else:
                print("   ❌ 音频文件未生成")
                
        except Exception as e:
            print(f"   ❌ 文件保存失败: {e}")
        
        # 测试中文
        print("7. 测试中文语音...")
        chinese_text = "这是中文测试，你好世界！"
        try:
            engine.say(chinese_text)
            engine.runAndWait()
            print("   ✅ 中文播放成功")
        except Exception as e:
            print(f"   ❌ 中文播放失败: {e}")
            
    except Exception as e:
        print(f"❌ TTS初始化失败: {e}")
        return False
    
    print("🎉 TTS测试完成")
    return True

if __name__ == "__main__":
    test_tts()
    input("按回车键退出...")
