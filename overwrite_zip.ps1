# 覆盖更新 dist/TTSReader_Windows_x64.zip
# 将最新 EXE 及文档收集到临时目录后整体压缩，避免路径兼容问题

$ErrorActionPreference = "Stop"

$zipPath = Join-Path "dist" "TTSReader_Windows_x64.zip"

# 创建临时目录
$baseTemp = [System.IO.Path]::GetTempPath()
if (-not $baseTemp -or -not (Test-Path $baseTemp)) { $baseTemp = $PSScriptRoot }
$staging = Join-Path $baseTemp ("TTSReader_Zip_" + [Guid]::NewGuid().ToString("N"))
New-Item -ItemType Directory -Force -Path $staging | Out-Null

$copied = 0

# 必选：EXE
$exe = "dist/TTSReader.exe"
if (Test-Path $exe) { Copy-Item $exe $staging; $copied++ }

# 可选：快速开始、README、LICENSE
$qs = "dist/快速开始-三步说明.txt"
if (Test-Path $qs) { Copy-Item $qs $staging; $copied++ }

$readme = "README.md"
if (Test-Path $readme) { Copy-Item $readme $staging; $copied++ }

$license = "LICENSE"
if (Test-Path $license) { Copy-Item $license $staging; $copied++ }

if ($copied -eq 0) {
  Remove-Item $staging -Recurse -Force -ErrorAction SilentlyContinue
  Write-Error "没有可打包的文件（未找到 dist/TTSReader.exe 等）"
  exit 1
}

if (Test-Path $zipPath) { Remove-Item $zipPath -Force }

Compress-Archive -Path (Join-Path $staging '*') -DestinationPath $zipPath -Force

# 清理临时目录（容错重试）
$maxTries = 5
for ($i=0; $i -lt $maxTries; $i++) {
  try { Remove-Item $staging -Recurse -Force -ErrorAction Stop; break } catch { Start-Sleep -Milliseconds 300 }
}

Write-Host ("Updated: " + $zipPath)

