# -*- coding: utf-8 -*-
"""
系统配置管理
- 提供全局配置参数
- 支持环境变量覆盖
- 支持配置文件加载
"""
from typing import Optional, Dict, Any
from pathlib import Path
import json
import os


class Settings:
    """系统设置类"""
    
    def __init__(self):
        # 基础路径配置
        self.BASE_DIR = Path(__file__).resolve().parent.parent.parent.parent
        self.CACHE_DIR = self.BASE_DIR / "temp" / "cache"
        self.DB_DIR = self.BASE_DIR / "data"
        
        # 确保目录存在
        self.CACHE_DIR.mkdir(parents=True, exist_ok=True)
        self.DB_DIR.mkdir(parents=True, exist_ok=True)
        
        # 数据库配置
        self.DATABASE_URL = os.getenv(
            "DATABASE_URL", 
            f"sqlite:///{self.DB_DIR}/tts_books.db"
        )
        
        # TTS引擎配置
        self.DEFAULT_TTS_ENGINE = os.getenv("DEFAULT_TTS_ENGINE", "local")  # local or cloud
        self.DEFAULT_VOICE = os.getenv("DEFAULT_VOICE", "zh-CN-XiaoxiaoNeural")
        self.DEFAULT_RATE = int(os.getenv("DEFAULT_RATE", "200"))
        self.DEFAULT_VOLUME = float(os.getenv("DEFAULT_VOLUME", "1.0"))
        
        # 音频配置
        self.AUDIO_FORMAT = os.getenv("AUDIO_FORMAT", "wav")  # wav or mp3
        self.AUDIO_SAMPLE_RATE = int(os.getenv("AUDIO_SAMPLE_RATE", "22050"))
        self.AUDIO_BITRATE = os.getenv("AUDIO_BITRATE", "128k")
        self.AUDIO_CHANNELS = int(os.getenv("AUDIO_CHANNELS", "1"))  # 1=mono, 2=stereo
        
        # 文本处理配置
        self.MAX_FILE_SIZE_MB = int(os.getenv("MAX_FILE_SIZE_MB", "100"))
        self.DEFAULT_CHUNK_SIZE = int(os.getenv("DEFAULT_CHUNK_SIZE", "2000"))
        self.CHAPTER_REGEX = os.getenv(
            "CHAPTER_REGEX", 
            r'^(第[一二三四五六七八九十百千万0-9]+章.*?)$'
        )
        
        # 性能配置
        self.MAX_CONCURRENT_SYNTHESIS = int(os.getenv("MAX_CONCURRENT_SYNTHESIS", "2"))
        self.SYNTHESIS_TIMEOUT_SECONDS = int(os.getenv("SYNTHESIS_TIMEOUT_SECONDS", "300"))
        self.CACHE_MAX_SIZE_GB = float(os.getenv("CACHE_MAX_SIZE_GB", "10"))
        
        # API配置
        self.API_HOST = os.getenv("API_HOST", "127.0.0.1")
        self.API_PORT = int(os.getenv("API_PORT", "8000"))
        self.CORS_ORIGINS = os.getenv(
            "CORS_ORIGINS", 
            "http://localhost:5173,http://127.0.0.1:5173,http://localhost:8080"
        ).split(",")
        
        # 日志配置
        self.LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO")
        self.LOG_FILE = self.BASE_DIR / "logs" / "tts_app.log"
        self.LOG_FILE.parent.mkdir(parents=True, exist_ok=True)
        
        # 功能开关
        self.ENABLE_CLOUD_TTS = os.getenv("ENABLE_CLOUD_TTS", "true").lower() == "true"
        self.ENABLE_AUTO_CACHE = os.getenv("ENABLE_AUTO_CACHE", "true").lower() == "true"
        self.ENABLE_BATCH_SYNTHESIS = os.getenv("ENABLE_BATCH_SYNTHESIS", "false").lower() == "true"
        
        # 加载用户配置文件（如果存在）
        self._load_user_config()
    
    def _load_user_config(self):
        """加载用户配置文件"""
        config_file = self.BASE_DIR / "config.json"
        if config_file.exists():
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    user_config = json.load(f)
                    for key, value in user_config.items():
                        if hasattr(self, key):
                            setattr(self, key, value)
            except Exception as e:
                print(f"加载配置文件失败: {e}")
    
    def save_config(self) -> bool:
        """保存当前配置到文件"""
        config_file = self.BASE_DIR / "config.json"
        try:
            config_dict = {
                "DEFAULT_TTS_ENGINE": self.DEFAULT_TTS_ENGINE,
                "DEFAULT_VOICE": self.DEFAULT_VOICE,
                "DEFAULT_RATE": self.DEFAULT_RATE,
                "DEFAULT_VOLUME": self.DEFAULT_VOLUME,
                "AUDIO_FORMAT": self.AUDIO_FORMAT,
                "AUDIO_SAMPLE_RATE": self.AUDIO_SAMPLE_RATE,
                "AUDIO_BITRATE": self.AUDIO_BITRATE,
                "MAX_FILE_SIZE_MB": self.MAX_FILE_SIZE_MB,
                "DEFAULT_CHUNK_SIZE": self.DEFAULT_CHUNK_SIZE,
                "CHAPTER_REGEX": self.CHAPTER_REGEX,
                "ENABLE_CLOUD_TTS": self.ENABLE_CLOUD_TTS,
                "ENABLE_AUTO_CACHE": self.ENABLE_AUTO_CACHE,
                "ENABLE_BATCH_SYNTHESIS": self.ENABLE_BATCH_SYNTHESIS,
            }
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config_dict, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            print(f"保存配置文件失败: {e}")
            return False
    
    def get_cache_path(self, book_id: int, chapter_idx: int, format: str = None) -> Path:
        """获取章节音频缓存路径"""
        format = format or self.AUDIO_FORMAT
        return self.CACHE_DIR / f"book_{book_id}" / f"ch_{chapter_idx}.{format}"
    
    def get_temp_path(self, filename: str) -> Path:
        """获取临时文件路径"""
        temp_dir = self.BASE_DIR / "temp" / "processing"
        temp_dir.mkdir(parents=True, exist_ok=True)
        return temp_dir / filename
    
    def clean_old_cache(self, max_size_gb: Optional[float] = None) -> int:
        """清理旧缓存文件
        
        Args:
            max_size_gb: 最大缓存大小（GB），None则使用配置值
        
        Returns:
            删除的文件数量
        """
        max_size_gb = max_size_gb or self.CACHE_MAX_SIZE_GB
        max_size_bytes = max_size_gb * 1024 * 1024 * 1024
        
        # 获取所有缓存文件并按修改时间排序
        cache_files = []
        total_size = 0
        
        for file_path in self.CACHE_DIR.rglob("*"):
            if file_path.is_file():
                stat = file_path.stat()
                cache_files.append((file_path, stat.st_mtime, stat.st_size))
                total_size += stat.st_size
        
        # 按修改时间排序（最旧的在前）
        cache_files.sort(key=lambda x: x[1])
        
        # 删除最旧的文件直到总大小低于限制
        deleted_count = 0
        for file_path, _, file_size in cache_files:
            if total_size <= max_size_bytes:
                break
            try:
                file_path.unlink()
                total_size -= file_size
                deleted_count += 1
            except Exception:
                pass
        
        return deleted_count
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            key: value for key, value in self.__dict__.items()
            if not key.startswith('_') and not callable(value)
        }


# 全局设置实例
settings = Settings()
