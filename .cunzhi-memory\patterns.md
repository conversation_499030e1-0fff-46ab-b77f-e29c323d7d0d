# 常用模式和最佳实践

- 寸止MCP工具配置最佳实践：1. 配置前先检查git仓库环境，如无则运行git init初始化；2. 配置提示词后必须测试对话拦截和记忆管理两个核心功能；3. Windows环境下注意路径格式问题，参考GitHub Issue #30；4. 与interactive_feedback MCP工具协同使用无冲突，功能互补；5. 记忆管理使用"请记住："指令触发，按category分类存储
- Obsidian Dataview搜索系统开发最佳实践：

1. **避免HTML链接崩溃问题**：
   - 绝对不要在dataviewjs中使用`<a href="${result.link}">`
   - 使用onclick事件+JavaScript函数代替HTML链接
   - 通过app.workspace.openLinkText()安全打开文件

2. **渐进式开发策略**：
   - 先创建MVP版本确保基础稳定性
   - 逐步添加功能，每次只增加一个特性
   - 每次添加功能后都要测试稳定性

3. **性能优化原则**：
   - 限制搜索结果数量（50-100个）
   - 使用小批次处理（20个文件/批）
   - 避免同时读取大量文件内容
   - 简化DOM结构减少内存泄漏

4. **错误处理机制**：
   - 使用try-catch包装所有文件操作
   - 跳过有问题的文件而不是崩溃
   - 提供多重降级方案

5. **代码结构要求**：
   - 将复杂函数拆分为简单函数
   - 避免深层嵌套的异步操作
   - 使用window对象暴露全局函数供onclick调用
- MVP搜索系统渐进式升级经验：

v1.0 → v1.1 → v1.2 成功升级路径：

1. **v1.0 基础版**：
   - 解决崩溃问题（onclick代替href）
   - 基础搜索功能
   - 稳定性优先

2. **v1.1 增强版**：
   - 添加搜索模式（OR/AND/精确匹配）
   - 添加搜索范围（文件名/内容/全部）
   - 保持稳定性基础

3. **v1.2 专业版**：
   - 添加文件类型过滤（Markdown/图片/PDF/其他）
   - 添加结果排序（名称/时间/大小/相关性）
   - 增强界面显示（文件图标、大小、相关性分数）

关键成功因素：
- 每次只添加1-2个功能
- 保持核心稳定性机制不变
- 渐进式测试，确保每个版本都能工作
- 用户反馈驱动的功能优先级
- MVP搜索系统v1.3高级功能开发经验：

第三阶段功能实现：

1. **智能搜索建议系统**：
   - 基于搜索历史的智能建议
   - 热门关键词推荐（基于使用频率）
   - 防抖输入处理（300ms延迟）
   - 键盘导航支持（Enter确认，Escape取消）

2. **搜索历史管理**：
   - localStorage持久化存储
   - 最近20条搜索记录
   - 完整搜索参数保存（关键词、模式、范围、类型、排序）
   - 一键恢复历史搜索

3. **搜索统计分析**：
   - 总搜索次数统计
   - 不同关键词数量
   - 平均结果数计算
   - 热门关键词Top 10排行
   - 可视化进度条显示

4. **用户体验优化**：
   - 实时搜索建议下拉框
   - 点击外部自动隐藏建议
   - 悬浮效果和视觉反馈
   - 统计数据可视化展示

技术要点：
- 使用localStorage进行数据持久化
- 防抖技术优化输入响应
- 全局函数暴露供onclick调用
- 事件委托和键盘交互支持
- MVP搜索系统v1.4性能优化和功能修复经验：

问题分析和解决方案：

1. **性能问题解决**：
   - 简化相关性计算算法（减少复杂度）
   - 优化批处理机制（每20个文件更新状态）
   - 减少不必要的DOM操作和统计更新
   - 移除复杂的搜索建议实时计算

2. **结果数量限制修复**：
   - 添加可选择的结果限制（50/100/200/无限制）
   - 用户可根据需要调整搜索结果数量
   - 默认设置为100个结果平衡性能和完整性

3. **目录自适应功能恢复**：
   - 实现自动检测vault目录结构
   - 动态生成目录选择选项
   - 排除系统目录（.obsidian、Templates）
   - 错误处理和降级方案

4. **内容预览功能恢复**：
   - 恢复文件内容片段提取
   - 智能上下文提取（匹配行+前后1行）
   - 关键词高亮显示
   - 片段长度限制（200字符）

关键优化技术：
- 简化算法复杂度
- 减少localStorage操作频率
- 优化DOM更新策略
- 保持核心稳定性机制
- Obsidian搜索系统文件打开方式修复经验：

用户需求：点击文件链接在新标签页中打开，而不是当前页面

解决方案：

1. **主要方法 - getLeaf('tab')**：
   ```javascript
   const newLeaf = app.workspace.getLeaf('tab');
   newLeaf.openFile(file);
   app.workspace.setActiveLeaf(newLeaf);
   ```

2. **备用方法 - splitActiveLeaf**：
   ```javascript
   const newLeaf = app.workspace.splitActiveLeaf();
   newLeaf.openFile(file);
   ```

3. **降级方案 - Obsidian URI**：
   ```javascript
   const obsidianUri = `obsidian://open?vault=${vaultName}&file=${filePath}`;
   window.open(obsidianUri, '_blank');
   ```

4. **用户体验优化**：
   - 添加↗️图标提示新标签页打开
   - 添加title属性说明
   - 在搜索结果顶部添加使用说明
   - 错误时提供文件路径复制功能

关键技术点：
- 使用getLeaf('tab')创建新标签页
- setActiveLeaf确保新标签页获得焦点
- 多重降级方案确保兼容性
- 用户友好的错误处理和提示
- 通用笔记搜索系统崩溃问题修复经验：

基于MVP搜索系统-优化版的成功经验，成功修复通用笔记搜索系统的崩溃问题：

1. **核心修复方案**：
   - 完全移植MVP优化版的safeOpenFile函数
   - 使用getLeaf('tab')在新标签页中打开文件
   - 多重降级方案确保兼容性
   - 添加用户体验优化（↗️图标、悬浮提示）

2. **保持原有功能特性**：
   - 自适应目录检测功能
   - 高级搜索选项（文件类型、排序方式）
   - 异步分批处理和进度显示
   - 智能上下文提取和内容预览

3. **技术移植要点**：
   - 保持相同的文件打开逻辑
   - 统一的错误处理机制
   - 一致的用户界面提示
   - 相同的兼容性保证

4. **修复验证**：
   - 文件链接在新标签页打开
   - 不会导致Obsidian崩溃
   - 保持所有原有功能正常工作
   - 用户体验得到改善

关键成功因素：
- 直接移植已验证的解决方案
- 保持核心修复逻辑不变
- 完整的功能特性保留
- 统一的用户体验标准
- Obsidian文件链接崩溃问题完整解决方案：

根因分析：
- HTML链接`<a href="${result.link}">`与Obsidian内部文件路由机制冲突
- dataviewjs环境中的DOM事件处理与Obsidian核心功能冲突
- 直接使用文件路径作为href导致应用程序级别的异常

技术解决方案：
```javascript
// 安全的文件打开函数 - 多重降级方案
function safeOpenFile(filePath) {
    // 方法1：getLeaf('tab') - 主要方案
    const newLeaf = app.workspace.getLeaf('tab');
    newLeaf.openFile(file);
    app.workspace.setActiveLeaf(newLeaf);
    
    // 方法2：splitActiveLeaf - 备用方案
    const newLeaf = app.workspace.splitActiveLeaf();
    newLeaf.openFile(file);
    
    // 方法3：openLinkText - 降级方案
    app.workspace.openLinkText(filePath, '', false);
    
    // 方法4：Obsidian URI - 最终降级
    const obsidianUri = `obsidian://open?vault=${vaultName}&file=${filePath}`;
    window.open(obsidianUri, '_blank');
}
```

关键技术要点：
- 完全避免HTML href属性
- 使用onclick事件处理文件打开
- 通过window对象暴露全局函数
- 新标签页打开避免影响当前工作流
- 多重降级确保各种环境兼容性
- Obsidian工具开发渐进式MVP方法论：

核心原则：
1. 稳定性优先 - 每个版本都必须能正常工作
2. 单一功能增量 - 每次只添加1-2个新功能
3. 用户反馈驱动 - 根据实际使用反馈调整优先级
4. 向后兼容 - 新功能不能破坏已有功能

标准开发流程：
v1.0 基础版：
- 解决核心痛点（崩溃问题）
- 实现最小可用功能
- 确保基础稳定性

v1.1 增强版：
- 添加1-2个核心功能
- 保持原有稳定性
- 用户测试验证

v1.2 专业版：
- 添加高级功能选项
- 增强用户界面
- 性能优化

v1.3 高级版：
- 智能化功能
- 用户体验优化
- 数据持久化

v1.4 优化版：
- 性能问题修复
- 功能完整性恢复
- 最终稳定版本

关键成功因素：
- 每次迭代后都要求用户测试
- 保持核心架构不变
- 渐进式复杂度增加
- 完整的错误处理机制
- 详细的版本记录和回滚能力
- 成功技术方案跨系统移植最佳实践：

移植原则：
1. 完整性移植 - 不要部分移植，要完整复制整个解决方案
2. 核心逻辑不变 - 保持已验证的核心修复逻辑完全一致
3. 适配性调整 - 根据目标系统特点进行必要的接口适配
4. 功能特性保留 - 确保目标系统原有功能完全保留

标准移植流程：
1. 问题验证 - 确认目标系统存在相同问题
2. 方案分析 - 分析源系统解决方案的核心要素
3. 完整移植 - 复制整个解决方案而非部分功能
4. 接口适配 - 调整函数名、参数等以适配目标系统
5. 功能测试 - 验证移植后的功能完整性
6. 稳定性测试 - 确保不会引入新的问题

成功案例：MVP优化版 → 通用系统修复版
- 完全移植safeOpenFile函数
- 保持4种降级方案不变
- 适配目标系统的界面结构
- 保留所有原有高级功能
- 统一用户体验标准

关键成功因素：
- 直接移植已验证的解决方案
- 避免重新发明轮子
- 保持技术方案的一致性
- 完整的测试验证流程
- 详细的移植记录和文档
- Obsidian工具用户体验优化标准模式：

视觉提示设计原则：
1. 功能暗示 - 通过图标和文字明确告知用户操作结果
2. 状态反馈 - 提供清晰的操作状态和进度信息
3. 错误友好 - 失败时提供有用的错误信息和解决建议
4. 一致性 - 保持整个系统的视觉和交互一致性

标准UX优化元素：
1. 操作提示图标：
   - ↗️ 表示新标签页打开
   - 🔄 表示正在处理
   - ✅ 表示操作成功
   - ❌ 表示操作失败

2. 悬浮说明（title属性）：
   - "点击在新标签页中打开文件"
   - "正在搜索中，请稍候"
   - "搜索完成，点击查看结果"

3. 使用指南文字：
   - 在界面顶部提供操作说明
   - 在结果区域说明交互方式
   - 在错误时提供解决建议

4. 错误处理优化：
   - 提供文件路径复制功能
   - 显示详细错误信息
   - 给出替代解决方案

实施标准：
- 所有可点击元素都要有视觉提示
- 所有异步操作都要有进度反馈
- 所有错误都要有友好的用户提示
- 所有新功能都要有使用说明

用户体验验证清单：
□ 用户能否一眼看出可以点击的元素
□ 用户能否理解点击后会发生什么
□ 用户能否知道操作是否成功
□ 用户遇到错误时能否知道如何解决
- Obsidian搜索系统汇总导出最佳实践：1)数据结构匹配-搜索结果使用直接属性(result.name, result.path)而非嵌套结构(result.file.name)；2)简洁双链格式-使用[[文件路径|文件名]]格式，添加匹配类型和相关性分数；3)精简代码结构-移除调试代码，保留核心功能(检查结果→生成内容→创建文件→打开文件)；4)文件夹管理-创建后询问用户是否移动到search-results文件夹；5)错误处理-主流程失败时降级创建临时文件；6)用户体验-显示处理状态，提供清晰的成功/失败提示。
- Obsidian API最佳实践：1)新标签页创建使用app.workspace.getLeaf('tab')然后openFile()；2)文件操作异步错误处理采用try-catch嵌套，主流程失败时降级创建临时文件；3)文件夹检测使用getAbstractFileByPath()，不存在时用createFolder()创建；4)文件重命名用rename()方法，需处理路径冲突；5)API可用性检查顺序：app存在→app.vault存在→app.workspace存在。
- 搜索相关性算法优化：1)TF-IDF计算适用于小规模文档集合，使用词频和逆文档频率；2)匹配类型权重分配：文件名匹配>标题匹配>内容匹配；3)相关性分数计算包含关键词密度、位置权重、匹配完整性；4)实时搜索使用防抖动(debounce)机制，延迟300-500ms执行；5)大量文件处理时分批处理，避免界面阻塞。
- 搜索数据结构设计模式：1)搜索结果使用扁平化结构{name, path, matchType, relevanceScore}，避免深层嵌套；2)文件类型检测基于扩展名映射，支持md/txt/pdf等常见格式；3)搜索状态管理使用单一对象{results, keywords, settings}，便于导出和恢复；4)大文件内容截取使用snippet字段，限制长度避免内存占用；5)异步处理使用Promise.all()并行处理多文件，提升性能。
- 文件管理体验设计模式：1)自动文件夹创建：检测目标文件夹存在性，不存在时静默创建；2)用户选择权保护：重要操作(移动文件)提供确认选项，默认不强制执行；3)文件命名策略：使用时间戳避免冲突，包含关键词便于识别；4)降级处理机制：主文件名冲突时创建临时文件，提示用户手动重命名；5)操作反馈及时性：文件操作完成后立即显示结果路径。
- 错误处理分层策略：1)三层处理机制：主流程(正常创建文件)→降级流程(临时文件)→最终兜底(错误提示)；2)错误信息分级：技术错误记录到console，用户错误显示友好提示；3)异常恢复原则：尽量保持功能可用，避免完全失败；4)错误上下文保存：记录操作参数便于问题排查；5)用户引导策略：提供具体的解决建议而非抽象错误码。
- Claude Code安装配置最佳实践：
1. 技能进步模式：从脚本依赖→命令行熟练→主动清理冗余文件，体现了良好的学习轨迹
2. 配置要点：使用问问AI服务(https://code.wenwen-ai.com/v1)，需要API Key和BaseURL配置
3. 常见问题：API 403配额错误，即使余额充足仍可能提示配额不足，需联系官方客服解决配额管理
4. 备选方案：准备Gemini-2.5 API key作为替代方案，确保工具链的连续性
5. 项目管理：询问要不要清理不需要的安装脚本、启动脚本、配置文件，保持项目整洁
6. 工具演进：从自动化脚本辅助→直接PowerShell命令行操作，提升技术独立性
- CLI工具持久化配置最佳实践：
1. 配置层次：临时配置($env:VAR)→持久化配置([Environment]::SetEnvironmentVariable)→验证加载→一键启动
2. 环境变量设置模式：[Environment]::SetEnvironmentVariable("KEY", "VALUE", "User")用于用户级持久化，避免影响系统其他用户
3. 当前会话加载：持久化设置后必须手动加载到当前会话$env:VAR = [Environment]::GetEnvironmentVariable("VAR", "User")
4. 配置验证方法：Get-ChildItem Env: | Where-Object Name -like "*关键字*"检查环境变量是否正确设置
5. 一次配置永久使用：正确的持久化配置后，新开PowerShell窗口可直接使用命令，无需重复设置
6. 安全实践：敏感信息如API Key在显示时使用.Substring(0,10)...方式部分隐藏
- Windows Terminal离线安装最佳实践模式：1)先安装Microsoft.UI.Xaml依赖框架 2)再安装主程序msixbundle文件 3)使用PowerShell Add-AppPackage命令 4)完整路径避免解析错误 5)监控进度条和返回码验证成功 6)适合企业环境离线批量部署
- 技术工具安装指导标准化流程模式：1)分析官方文档和社区教程获取权威信息 2)检查本地文件完整性确保安装包可用 3)验证系统要求和依赖关系避免兼容性问题 4)按正确顺序执行安装命令遵循依赖关系 5)验证安装结果确保功能正常 6)提供配置优化建议提升用户体验
- TTSReader 打包与分发最佳实践：1) 打包依赖管理：当 PyInstaller 构建的 EXE 缺少 PDF/DOCX/EPUB 等功能时，先在构建环境安装相应依赖（PyPDF2、python-docx、ebooklib、beautifulsoup4、lxml），并在 .spec 的 hiddenimports 显式声明相关模块，最后使用 `pyinstaller --clean` 进行干净重建以确保依赖被正确打包；2) 前端错误处理：SPA 文件上传要为 fetch 添加完整错误处理（JSON 解析失败时降级到 text、检查 HTTP 状态码、展示用户友好的错误信息），避免“操作中断但无提示”；3) PowerShell 脚本化：避免复杂一行式命令，创建独立 .ps1 脚本，先复制文件到临时目录再整体压缩，并对删除/压缩加入重试以规避文件占用；4) 构建验证：每次重打包后使用 `Get-ChildItem dist | Select Name,Length,LastWriteTime` 验证大小与修改时间，确认构建确已更新。
