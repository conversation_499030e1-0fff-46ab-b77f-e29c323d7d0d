# 小说TXT转语音听书系统（MVP）设计方案（2025-08-08 星期五）

## Overview / 概述
- 目标：实现离线优先的TXT→TTS听书MVP，覆盖TXT导入与解析、文本预处理、TTS（本地优先SAPI5/pyttsx3，备选edge-tts）、语音参数控制、播放器控制、进度与书签、章节MP3缓存。
- 技术栈：前端 Electron + React + TypeScript；后端 FastAPI（Python）；本地数据库 SQLite；音频存储本地文件系统；Windows 10/11 优先。
- 设计原则：稳定优先、可离线、渐进增强（可切换云TTS）、章节级缓存、解耦前后端、错误可恢复。

## Architecture / 架构
- 分层：Frontend(Electron+React) ↔ IPC/HTTP ↔ Backend(FastAPI) ↔ SQLite/FS
- 模块：
  1) Importer：TXT导入/编码检测/分块读取
  2) Preprocessor：章节分割/标点与空白规范化/特殊字符处理
  3) Synthesizer：TTS队列（local SAPI5优先，cloud edge-tts可选）
  4) Cache Manager：章节MP3落地与索引
  5) Player Service：播放/暂停/快进/倒退/章节跳转（前端使用HTMLAudio或Electron音频模块）
  6) Progress Service：进度与书签持久化
  7) Settings：语速/音调/停顿/缓存策略/TTS选择

### 架构图（Mermaid）
```mermaid
flowchart LR
  UI[Electron+React UI] -- IPC/HTTP --> API[FastAPI Backend]
  API --> IMP[Importer]
  API --> PRE[Preprocessor]
  API --> SYN[Synthesizer]
  SYN -->|MP3| CACHE[Cache Manager]
  UI -->|play| CACHE
  API --> DB[(SQLite)]
  CACHE --> FS[/Local FS/]
```

## Components & Interfaces / 组件与接口
- Importer（后端）
  - 接口：POST /import
    - 入参：filePath: string
    - 出参：bookId, meta{encoding, size}, stats
  - 说明：自动编码检测（UTF-8/GBK），失败则返回提示；大文件流式读取。

- Preprocessor（后端）
  - 接口：POST /preprocess
    - 入参：bookId, rules{chapterRegex?, fallbackChunkSize?}
    - 出参：chapters[{id, title, startOffset, endOffset, estDurationSec}]
  - 说明：正则分章失败→固定字数/段落降级，标点/空白规范化，清理异常字符。

- Synthesizer（后端）
  - 接口：POST /synthesize
    - 入参：bookId, chapterId, voice{engine:"local|edge", rate, pitch, pauseCfg}, forceRegen?:boolean
    - 出参：audioPath, durationSec
  - 说明：任务入队；本地pyttsx3默认，edge-tts可选；失败有限重试；生成MP3。

- Cache Manager（后端）
  - 接口：GET /cache/status?bookId
    - 出参：cachedChapters: string[]，spaceStats
  - 说明：LRU/阈值清理策略（后续迭代可加）；索引表维护chapter→filePath。

- Player Service（前端）
  - 播放：HTMLAudio或AudioContext；
  - 控制：play/pause/seek(step=5–15s configurable)/chapterJump；
  - 预加载：若缓存命中，500ms内开始播放；未命中则显示“生成/加载”。

- Progress Service（前端→后端）
  - 接口：POST /progress/save（bookId, chapterId, positionMs）
  - 接口：GET /progress/last?bookId
  - 接口：POST /bookmark/add（bookId, chapterId, positionMs, title?, note?）

- Settings（前端）
  - 本地设置项：rate/pitch/pause、engine优先级、缓存策略、快捷键。

## Data Models / 数据模型（SQLite）
- books(id, title, file_path, encoding, size_bytes, created_at)
- chapters(id, book_id, idx, title, start_offset, end_offset, est_duration_sec, cached_path?)
- progress(id, book_id, chapter_id, position_ms, updated_at)
- bookmarks(id, book_id, chapter_id, position_ms, name, note, created_at)
- settings(id, key, value_json)

## Data Flow / 处理流程
1) 导入：UI选择TXT → /import → 入库书籍与元数据
2) 预处理：/preprocess → 章节索引生成 → 入库chapters
3) 播放：选择章节 → 命中缓存则直接播放，否则触发/synthesize → 生成MP3 → 更新chapters.cached_path → 播放
4) 进度：播放中定时/关键事件 → /progress/save；重启后 /progress/last 恢复

## 性能与并发
- 文件读取：大文件分块（例如1–4MB块）
- 合成队列：限制并发（默认1–2并发），避免前端卡顿
- 播放优先：合成为后台低优先级任务，不阻断UI

## 错误处理 / Error Handling
- 分类：导入/预处理/合成/播放/存储
- 策略：
  - 可重试：合成失败重试N次，切换引擎降级
  - 友好提示：明确问题与解决建议
  - 日志：本地最小化日志；诊断模式提升详细度

## 安全与隐私
- 本地优先：默认不上传文本/音频
- 云TTS：仅在用户开启时调用edge-tts，并提示数据出网

## 测试策略（MVP级）
- 单元/集成：
  - 编码检测/分块读取的健壮性（人工样本）
  - 分章规则覆盖常见样式
  - TTS合成管线（本地SAPI5为主）
  - 播放控制与进度保存（前端交互）
- 手工验收：对照EARS标准逐项验收

## 关键设计决策与理由
- 本地优先TTS：保证离线可用与稳定性
- 章节级缓存：提升启动播放速度与复播体验
- FastAPI解耦：前后端分层清晰，便于替换合成实现
- SQLite：足够轻量且稳定，便于分发

## 未来可扩展
- 多引擎融合：Coqui-TTS/VITS 本地模型；ElevenLabs等云
- 更丰富的UI：批量预生成、章节队列、声音包管理
- 高级处理：标点/停顿智能化、朗读风格模板

