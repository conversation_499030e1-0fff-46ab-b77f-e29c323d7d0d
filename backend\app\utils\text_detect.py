# -*- coding: utf-8 -*-
"""文本与文件检测工具
- 编码检测：优先BOM→尝试UTF-8→退回GBK
- 换行风格检测：采样若干字节统计 \r\n 与 \n
- 备注：不引入第三方 chardet，MVP足够；后续可升级
"""
from __future__ import annotations
from pathlib import Path
from typing import Literal

ENCODING = Literal["UTF-8", "GBK"]


def detect_encoding_and_newlines(path: Path) -> dict:
    """检测TXT编码与换行风格。
    返回: { encoding: 'UTF-8'|'GBK', newline: 'CRLF'|'LF', bom: bool }
    """
    with path.open("rb") as f:
        head = f.read(4)
        bom_utf8 = head.startswith(b"\xef\xbb\xbf")
    if bom_utf8:
        return {"encoding": "UTF-8", "newline": _detect_newline(path), "bom": True}

    # 尝试UTF-8解码
    try:
        with path.open("r", encoding="utf-8") as fr:
            fr.read(4096)
        return {"encoding": "UTF-8", "newline": _detect_newline(path), "bom": False}
    except Exception:
        # 回退GBK
        return {"encoding": "GBK", "newline": _detect_newline(path), "bom": False}


def _detect_newline(path: Path) -> str:
    """粗略检测换行风格（采样前 64KB）。"""
    with path.open("rb") as f:
        sample = f.read(65536)
    crlf = sample.count(b"\r\n")
    lf = sample.count(b"\n")
    # 如果同时存在，按比例判断（近似）
    if crlf == 0 and lf == 0:
        return "LF"  # 无换行时默认LF
    # 注意：LF包含在CRLF统计中，这里用crlf与lf-crlf近似
    pure_lf = max(lf - crlf, 0)
    return "CRLF" if crlf >= pure_lf else "LF"


def get_file_size(path: Path) -> int:
    return path.stat().st_size


def guess_title(path: Path) -> str:
    """根据文件名推断标题（去扩展名）。"""
    name = path.name
    if "." in name:
        return name.rsplit(".", 1)[0]
    return name

