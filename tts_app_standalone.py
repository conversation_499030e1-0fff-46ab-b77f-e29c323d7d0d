#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TXT→TTS 听书系统 - 独立桌面应用
集成后端API + 内置Web界面，打包为单个EXE
"""
import os
import sys
import threading
import webbrowser
import tempfile
import shutil
from pathlib import Path
from typing import Optional, List, Dict, Any
import time
import json
import re
import logging

# 禁用所有日志输出，避免PyInstaller打包问题
logging.getLogger().setLevel(logging.CRITICAL)
logging.getLogger("uvicorn").setLevel(logging.CRITICAL)
logging.getLogger("uvicorn.access").setLevel(logging.CRITICAL)
logging.getLogger("uvicorn.error").setLevel(logging.CRITICAL)

# Web框架和TTS
from fastapi import FastAPI, HTTPException, UploadFile, File
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse, FileResponse
import uvicorn
import pyttsx3

# 尝试导入edge-tts以支持云端语音
try:
    import edge_tts
    import asyncio
    HAS_EDGE_TTS = True
except ImportError:
    HAS_EDGE_TTS = False

# 数据库（内存SQLite）
import sqlite3
from contextlib import contextmanager

# 音频处理
try:
    from pydub import AudioSegment
    HAS_PYDUB = True
except ImportError:
    HAS_PYDUB = False

# 文档处理
try:
    import docx
    HAS_DOCX = True
except ImportError:
    HAS_DOCX = False

try:
    import PyPDF2
    HAS_PDF = True
except ImportError:
    HAS_PDF = False

try:
    import ebooklib
    from ebooklib import epub
    from bs4 import BeautifulSoup
    HAS_EPUB = True
except ImportError:
    HAS_EPUB = False

class TTSApp:
    def __init__(self):
        self.app = FastAPI(title="TTS听书系统")
        # 使用文件数据库而不是内存数据库，避免数据丢失
        self.cache_dir = Path(tempfile.gettempdir()) / "tts_app_cache"
        self.cache_dir.mkdir(exist_ok=True)
        self.db_path = self.cache_dir / "tts_books.db"

        # 确保数据库初始化
        self.setup_database()
        self.setup_routes()

        # 设置静态文件服务（用于音频文件）
        self.app.mount("/audio-files", StaticFiles(directory=str(self.cache_dir)), name="audio-files")

        print("[INFO] TTS听书系统启动中...")
        print("[INFO] 访问地址: http://127.0.0.1:8080")
        print(f"[INFO] 支持格式: TXT, DOCX{'[OK]' if HAS_DOCX else '[X]'}, PDF{'[OK]' if HAS_PDF else '[X]'}, EPUB{'[OK]' if HAS_EPUB else '[X]'}")
        print(f"[INFO] 数据库路径: {self.db_path}")

        # 自动打开浏览器
        threading.Timer(2.0, lambda: webbrowser.open("http://127.0.0.1:8080")).start()
        
    def setup_database(self):
        """初始化数据库"""
        with self.get_db() as conn:
            conn.execute("""
                CREATE TABLE IF NOT EXISTS books (
                    id INTEGER PRIMARY KEY,
                    title TEXT,
                    file_path TEXT,
                    encoding TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            conn.execute("""
                CREATE TABLE IF NOT EXISTS chapters (
                    id INTEGER PRIMARY KEY,
                    book_id INTEGER,
                    idx INTEGER,
                    title TEXT,
                    start_offset INTEGER,
                    end_offset INTEGER,
                    cached_path TEXT,
                    FOREIGN KEY (book_id) REFERENCES books (id)
                )
            """)
            conn.execute("""
                CREATE TABLE IF NOT EXISTS progress (
                    id INTEGER PRIMARY KEY,
                    book_id INTEGER,
                    chapter_id INTEGER,
                    position_sec REAL,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (book_id) REFERENCES books (id)
                )
            """)
    
    @contextmanager
    def get_db(self):
        """数据库连接上下文管理器"""
        conn = sqlite3.connect(str(self.db_path), check_same_thread=False)
        conn.row_factory = sqlite3.Row
        try:
            yield conn
            conn.commit()
        except Exception:
            conn.rollback()
            raise
        finally:
            conn.close()
    
    def detect_encoding(self, file_path: Path) -> str:
        """检测文件编码"""
        encodings = ['utf-8', 'gbk', 'gb2312', 'utf-16']
        for encoding in encodings:
            try:
                with open(file_path, 'r', encoding=encoding) as f:
                    f.read(1024)  # 读取前1KB测试
                return encoding
            except (UnicodeDecodeError, UnicodeError):
                continue
        return 'utf-8'  # 默认

    def extract_text_from_file(self, file_path: Path, file_type: str) -> str:
        """从不同格式文件中提取文本"""
        try:
            if file_type == 'txt':
                encoding = self.detect_encoding(file_path)
                with open(file_path, 'r', encoding=encoding) as f:
                    return f.read()

            elif file_type == 'docx' and HAS_DOCX:
                doc = docx.Document(file_path)
                return '\n'.join([paragraph.text for paragraph in doc.paragraphs])

            elif file_type == 'pdf' and HAS_PDF:
                text = ""
                with open(file_path, 'rb') as f:
                    reader = PyPDF2.PdfReader(f)
                    for page in reader.pages:
                        text += page.extract_text() + '\n'
                return text

            elif file_type == 'epub' and HAS_EPUB:
                book = epub.read_epub(file_path)
                text = ""
                for item in book.get_items():
                    if item.get_type() == ebooklib.ITEM_DOCUMENT:
                        soup = BeautifulSoup(item.get_content(), 'html.parser')
                        text += soup.get_text() + '\n'
                return text

            else:
                raise ValueError(f"不支持的文件格式: {file_type}")

        except Exception as e:
            raise ValueError(f"文件解析失败: {str(e)}")

    def split_chapters(self, text: str, regex_pattern: str = None) -> List[Dict]:
        """分割章节"""
        if not regex_pattern:
            regex_pattern = r'^(第[一二三四五六七八九十百千万0-9]+章.*?)$'
        
        try:
            pattern = re.compile(regex_pattern, re.MULTILINE)
            matches = list(pattern.finditer(text))
            
            if not matches:
                # 降级：按固定长度分割
                chunk_size = 2000
                chapters = []
                for i in range(0, len(text), chunk_size):
                    chapters.append({
                        'title': f'第{len(chapters)+1}段',
                        'start_offset': i,
                        'end_offset': min(i + chunk_size, len(text))
                    })
                return chapters
            
            chapters = []
            for i, match in enumerate(matches):
                start = match.start()
                end = matches[i+1].start() if i+1 < len(matches) else len(text)
                chapters.append({
                    'title': match.group(1).strip(),
                    'start_offset': start,
                    'end_offset': end
                })
            return chapters
            
        except re.error:
            # 正则错误，降级处理
            return self.split_chapters(text, None)
    
    def synthesize_audio(self, text: str, output_path: Path, rate: int = 200) -> bool:
        """合成音频"""
        try:
            print(f"[SYNTH] 开始合成音频: {output_path}")
            print(f"[INFO] 文本长度: {len(text)} 字符")

            engine = pyttsx3.init()

            # 设置中文语音（如果可用）
            voices = engine.getProperty('voices')
            for voice in voices:
                if 'chinese' in voice.name.lower() or 'huihui' in voice.name.lower():
                    engine.setProperty('voice', voice.id)
                    print(f"[VOICE] 使用语音: {voice.name}")
                    break

            engine.setProperty('rate', rate)
            engine.setProperty('volume', 1.0)

            # 确保输出目录存在
            output_path.parent.mkdir(parents=True, exist_ok=True)

            # 删除旧文件
            if output_path.exists():
                output_path.unlink()

            engine.save_to_file(text, str(output_path))
            engine.runAndWait()

            # 检查文件是否生成且有内容
            if output_path.exists():
                file_size = output_path.stat().st_size
                print(f"[OK] 音频文件生成成功: {file_size} bytes")
                return file_size > 100  # 至少100字节
            else:
                print("[ERROR] 音频文件未生成")
                return False

        except Exception as e:
            print(f"[ERROR] TTS合成失败: {e}")
            return False
    
    def setup_routes(self):
        """设置API路由"""
        
        @self.app.get("/", response_class=HTMLResponse)
        async def index():
            return self.get_html_interface()
        
        @self.app.post("/import")
        async def import_book(file: UploadFile = File(...)):
            """导入文档文件"""
            try:
                # 检查文件格式
                file_ext = file.filename.lower().split('.')[-1]
                supported_formats = ['txt', 'docx', 'pdf', 'epub']

                if file_ext not in supported_formats:
                    raise HTTPException(400, f"仅支持 {', '.join(supported_formats.upper())} 文件")

                # 检查格式支持
                if file_ext == 'docx' and not HAS_DOCX:
                    raise HTTPException(400, "DOCX格式需要安装 python-docx: pip install python-docx")
                if file_ext == 'pdf' and not HAS_PDF:
                    raise HTTPException(400, "PDF格式需要安装 PyPDF2: pip install PyPDF2")
                if file_ext == 'epub' and not HAS_EPUB:
                    raise HTTPException(400, "EPUB格式需要安装 ebooklib beautifulsoup4: pip install ebooklib beautifulsoup4")

                # 保存文件
                temp_path = self.cache_dir / f"book_{int(time.time())}.{file_ext}"
                with open(temp_path, 'wb') as f:
                    content = await file.read()
                    f.write(content)

                # 提取文本内容
                text_content = self.extract_text_from_file(temp_path, file_ext)
                if not text_content.strip():
                    raise HTTPException(400, "文件内容为空或无法解析")

                # 存入数据库
                with self.get_db() as conn:
                    cursor = conn.execute(
                        "INSERT INTO books (title, file_path, encoding) VALUES (?, ?, ?)",
                        (file.filename, str(temp_path), file_ext)
                    )
                    book_id = cursor.lastrowid

                return {"bookId": book_id, "title": file.filename, "encoding": file_ext}
            except HTTPException:
                raise
            except Exception as e:
                raise HTTPException(500, f"导入失败: {str(e)}")
        
        @self.app.post("/preprocess/{book_id}")
        async def preprocess_book(book_id: int, regex_pattern: str = None):
            """预处理分章"""
            with self.get_db() as conn:
                book = conn.execute("SELECT * FROM books WHERE id = ?", (book_id,)).fetchone()
                if not book:
                    raise HTTPException(404, "书籍不存在")
                
                # 提取文本内容
                file_ext = book['encoding']  # 现在存储的是文件类型
                text = self.extract_text_from_file(Path(book['file_path']), file_ext)
                
                # 分割章节
                chapters = self.split_chapters(text, regex_pattern)
                
                # 清除旧章节
                conn.execute("DELETE FROM chapters WHERE book_id = ?", (book_id,))
                
                # 插入新章节
                for i, chapter in enumerate(chapters):
                    conn.execute(
                        "INSERT INTO chapters (book_id, idx, title, start_offset, end_offset) VALUES (?, ?, ?, ?, ?)",
                        (book_id, i+1, chapter['title'], chapter['start_offset'], chapter['end_offset'])
                    )
                
                return {"bookId": book_id, "chapters": len(chapters)}
        
        @self.app.get("/chapters/{book_id}")
        async def get_chapters(book_id: int):
            """获取章节列表"""
            with self.get_db() as conn:
                chapters = conn.execute(
                    "SELECT id, idx, title FROM chapters WHERE book_id = ? ORDER BY idx",
                    (book_id,)
                ).fetchall()
                return [dict(ch) for ch in chapters]
        
        @self.app.post("/synthesize/{book_id}/{chapter_id}")
        async def synthesize_chapter(book_id: int, chapter_id: int, rate: int = 200):
            """合成章节音频"""
            try:
                with self.get_db() as conn:
                    book = conn.execute("SELECT * FROM books WHERE id = ?", (book_id,)).fetchone()
                    chapter = conn.execute("SELECT * FROM chapters WHERE id = ? AND book_id = ?", (chapter_id, book_id)).fetchone()

                    if not book or not chapter:
                        raise HTTPException(404, "书籍或章节不存在")

                    # 检查缓存
                    if chapter['cached_path'] and Path(chapter['cached_path']).exists():
                        audio_filename = Path(chapter['cached_path']).name
                        return {"audioPath": f"/audio-files/{audio_filename}"}

                    # 提取完整文本内容
                    file_ext = book['encoding']  # 现在存储的是文件类型
                    full_text = self.extract_text_from_file(Path(book['file_path']), file_ext)

                    # 提取章节文本
                    text = full_text[chapter['start_offset']:chapter['end_offset']]

                    if not text.strip():
                        raise HTTPException(400, "章节内容为空")

                    # 合成音频
                    audio_path = self.cache_dir / f"ch_{chapter_id}.wav"
                    if self.synthesize_audio(text, audio_path, rate):
                        # 更新缓存路径
                        conn.execute("UPDATE chapters SET cached_path = ? WHERE id = ?", (str(audio_path), chapter_id))
                        return {"audioPath": f"/audio-files/{audio_path.name}"}
                    else:
                        raise HTTPException(500, "音频合成失败")
            except HTTPException:
                raise
            except Exception as e:
                raise HTTPException(500, f"合成失败: {str(e)}")
        
        @self.app.get("/audio/{book_id}/{chapter_id}")
        async def get_audio(book_id: int, chapter_id: int):
            """获取音频文件"""
            with self.get_db() as conn:
                chapter = conn.execute("SELECT cached_path FROM chapters WHERE id = ? AND book_id = ?", (chapter_id, book_id)).fetchone()
                if not chapter or not chapter['cached_path']:
                    raise HTTPException(404, "音频文件不存在")
                
                audio_path = Path(chapter['cached_path'])
                if not audio_path.exists():
                    raise HTTPException(404, "音频文件不存在")
                
                return FileResponse(audio_path, media_type="audio/wav")
    
    def get_html_interface(self) -> str:
        """返回内置HTML界面"""
        return """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TXT→TTS 听书系统</title>
    <style>
        body { font-family: 'Microsoft YaHei', sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .upload-area { border: 2px dashed #ccc; padding: 40px; text-align: center; margin-bottom: 20px; border-radius: 8px; }
        .upload-area:hover { border-color: #007bff; background: #f8f9fa; }
        .btn { padding: 10px 20px; margin: 5px; border: none; border-radius: 4px; cursor: pointer; font-size: 14px; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn:hover { opacity: 0.8; }
        .chapters { display: flex; gap: 20px; margin-top: 20px; }
        .chapter-list { flex: 1; max-height: 400px; overflow-y: auto; border: 1px solid #ddd; border-radius: 4px; }
        .chapter-item { padding: 10px; border-bottom: 1px solid #eee; cursor: pointer; }
        .chapter-item:hover { background: #f8f9fa; }
        .chapter-item.active { background: #007bff; color: white; }
        .player { flex: 1; padding: 20px; border: 1px solid #ddd; border-radius: 4px; }
        .controls { margin-top: 10px; }
        .progress { margin: 10px 0; }
        .status { margin: 10px 0; padding: 10px; border-radius: 4px; }
        .status.info { background: #d1ecf1; color: #0c5460; }
        .status.error { background: #f8d7da; color: #721c24; }
        input[type="file"] { display: none; }
        audio { width: 100%; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📚 TXT→TTS 听书系统</h1>
            <p>导入TXT/DOCX/PDF/EPUB小说，自动分章，语音朗读</p>
        </div>

        <div class="upload-area" onclick="document.getElementById('fileInput').click()">
            <p>📁 点击选择文档文件或拖拽文件到此处<br><small>支持: TXT, DOCX, PDF, EPUB</small></p>
            <input type="file" id="fileInput" accept=".txt,.docx,.pdf,.epub" onchange="uploadFile(this.files[0])">
        </div>
        
        <div id="status" class="status" style="display:none;"></div>
        
        <div id="bookInfo" style="display:none;">
            <h3>📖 <span id="bookTitle"></span></h3>
            <div>
                <input type="text" id="regexInput" placeholder="章节正则表达式（可选）" style="width: 300px; padding: 8px;">
                <button class="btn btn-primary" onclick="preprocessBook()">🔄 预处理分章</button>
            </div>
        </div>
        
        <div id="mainInterface" class="chapters" style="display:none;">
            <div class="chapter-list">
                <h4>📑 章节列表</h4>
                <div id="chapterList"></div>
            </div>
            
            <div class="player">
                <h4>🎵 播放器</h4>
                <div id="currentChapter">请选择章节</div>
                <audio id="audioPlayer" controls style="display:none;"></audio>
                <div class="controls">
                    <button class="btn btn-success" onclick="synthesizeAndPlay()" id="playBtn" disabled>🎤 合成并播放</button>
                    <button class="btn btn-primary" onclick="playPause()" id="pauseBtn" disabled>⏸️ 暂停</button>
                </div>
                <div class="progress">
                    <label>语速: <span id="rateValue">200</span></label>
                    <input type="range" id="rateSlider" min="100" max="300" value="200" oninput="updateRate(this.value)">
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentBookId = null;
        let currentChapterId = null;
        let currentRate = 200;
        
        function showStatus(message, type = 'info') {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = `status ${type}`;
            status.style.display = 'block';
            setTimeout(() => status.style.display = 'none', 3000);
        }
        
        async function uploadFile(file) {
            if (!file) return;

            showStatus('正在导入文件...');
            const formData = new FormData();
            formData.append('file', file);

            try {
                const response = await fetch('/import', {
                    method: 'POST',
                    body: formData
                });

                if (!response.ok) {
                    const errorText = await response.text();
                    try {
                        const errorJson = JSON.parse(errorText);
                        showStatus(errorJson.detail || '导入失败', 'error');
                    } catch {
                        showStatus('导入失败: ' + errorText, 'error');
                    }
                    return;
                }

                const result = await response.json();
                currentBookId = result.bookId;
                document.getElementById('bookTitle').textContent = result.title;
                document.getElementById('bookInfo').style.display = 'block';
                showStatus('文件导入成功！');
            } catch (error) {
                showStatus('导入失败: ' + error.message, 'error');
            }
        }
        
        async function preprocessBook() {
            if (!currentBookId) return;
            
            showStatus('正在预处理分章...');
            const regex = document.getElementById('regexInput').value;
            
            try {
                const url = `/preprocess/${currentBookId}${regex ? '?regex_pattern=' + encodeURIComponent(regex) : ''}`;
                const response = await fetch(url, { method: 'POST' });
                const result = await response.json();
                
                if (response.ok) {
                    showStatus(`分章完成，共${result.chapters}章`);
                    await loadChapters();
                    document.getElementById('mainInterface').style.display = 'flex';
                } else {
                    showStatus(result.detail || '预处理失败', 'error');
                }
            } catch (error) {
                showStatus('预处理失败: ' + error.message, 'error');
            }
        }
        
        async function loadChapters() {
            if (!currentBookId) return;
            
            try {
                const response = await fetch(`/chapters/${currentBookId}`);
                const chapters = await response.json();
                
                const listEl = document.getElementById('chapterList');
                listEl.innerHTML = chapters.map(ch => 
                    `<div class="chapter-item" onclick="selectChapter(${ch.id}, '${ch.title}')">${ch.idx}. ${ch.title}</div>`
                ).join('');
            } catch (error) {
                showStatus('加载章节失败: ' + error.message, 'error');
            }
        }
        
        function selectChapter(chapterId, title) {
            currentChapterId = chapterId;
            document.getElementById('currentChapter').textContent = `当前章节: ${title}`;
            document.getElementById('playBtn').disabled = false;
            
            // 更新选中状态
            document.querySelectorAll('.chapter-item').forEach(el => el.classList.remove('active'));
            event.target.classList.add('active');
        }
        
        async function synthesizeAndPlay() {
            if (!currentBookId || !currentChapterId) return;
            
            showStatus('正在合成音频...');
            document.getElementById('playBtn').disabled = true;
            
            try {
                const response = await fetch(`/synthesize/${currentBookId}/${currentChapterId}?rate=${currentRate}`, {
                    method: 'POST'
                });
                const result = await response.json();
                
                if (response.ok) {
                    const audioPlayer = document.getElementById('audioPlayer');
                    audioPlayer.src = `/audio/${currentBookId}/${currentChapterId}`;
                    audioPlayer.style.display = 'block';
                    audioPlayer.play();
                    document.getElementById('pauseBtn').disabled = false;
                    showStatus('开始播放');
                } else {
                    showStatus(result.detail || '合成失败', 'error');
                }
            } catch (error) {
                showStatus('合成失败: ' + error.message, 'error');
            } finally {
                document.getElementById('playBtn').disabled = false;
            }
        }
        
        function playPause() {
            const audio = document.getElementById('audioPlayer');
            if (audio.paused) {
                audio.play();
                document.getElementById('pauseBtn').textContent = '⏸️ 暂停';
            } else {
                audio.pause();
                document.getElementById('pauseBtn').textContent = '▶️ 播放';
            }
        }
        
        function updateRate(value) {
            currentRate = parseInt(value);
            document.getElementById('rateValue').textContent = value;
        }
        
        // 拖拽上传
        document.addEventListener('dragover', e => e.preventDefault());
        document.addEventListener('drop', e => {
            e.preventDefault();
            const files = e.dataTransfer.files;
            if (files.length > 0 && files[0].name.endsWith('.txt')) {
                uploadFile(files[0]);
            }
        });
    </script>
</body>
</html>
        """
    
    def run(self, host="127.0.0.1", port=8080):
        """启动应用"""
        print(f"[START] TTS听书系统启动中...")
        print(f"[URL] 访问地址: http://{host}:{port}")
        
        # 延迟打开浏览器
        def open_browser():
            time.sleep(2)
            webbrowser.open(f"http://{host}:{port}")
        
        threading.Thread(target=open_browser, daemon=True).start()
        
        # 启动服务器（完全静默模式）
        uvicorn.run(
            self.app,
            host=host,
            port=port,
            log_level="critical",
            access_log=False,
            use_colors=False
        )

def main():
    """主函数"""
    try:
        # 重定向标准输出到空设备（避免控制台输出）
        if getattr(sys, 'frozen', False):  # 如果是打包后的exe
            sys.stdout = open(os.devnull, 'w')
            sys.stderr = open(os.devnull, 'w')

        app = TTSApp()
        app.run()
    except KeyboardInterrupt:
        pass  # 静默退出
    except Exception as e:
        # 只在开发模式下显示错误
        if not getattr(sys, 'frozen', False):
            print(f"[ERROR] 启动失败: {e}")
            input("按回车键退出...")

if __name__ == "__main__":
    main()
