# -*- coding: utf-8 -*-
"""数据库模型定义（MVP）
- books / chapters / progress / bookmarks / settings
- 中文注释，字段尽量直观
"""
from __future__ import annotations
from datetime import datetime
from typing import Optional

from sqlalchemy import Column, Integer, String, DateTime, ForeignKey
from sqlalchemy.orm import declarative_base, relationship

Base = declarative_base()


class Book(Base):
    """书籍表：存放基础元数据。"""
    __tablename__ = "books"
    id = Column(Integer, primary_key=True, index=True)
    title = Column(String(512), nullable=True)  # TXT无标题则可为空
    file_path = Column(String(2048), nullable=False)
    encoding = Column(String(32), nullable=True)
    size_bytes = Column(Integer, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)

    chapters = relationship("Chapter", back_populates="book")


class Chapter(Base):
    """章节表：章节索引与缓存路径。"""
    __tablename__ = "chapters"
    id = Column(Integer, primary_key=True, index=True)
    book_id = Column(Integer, ForeignKey("books.id"), index=True, nullable=False)
    idx = Column(Integer, nullable=False)  # 章节序号
    title = Column(String(512), nullable=True)
    start_offset = Column(Integer, nullable=True)
    end_offset = Column(Integer, nullable=True)
    est_duration_sec = Column(Integer, nullable=True)
    cached_path = Column(String(2048), nullable=True)  # 生成后的MP3路径

    book = relationship("Book", back_populates="chapters")


class Progress(Base):
    """进度表：记录每本书最后播放位置。"""
    __tablename__ = "progress"
    id = Column(Integer, primary_key=True, index=True)
    book_id = Column(Integer, index=True, nullable=False)
    chapter_id = Column(Integer, index=True, nullable=True)
    position_ms = Column(Integer, nullable=False, default=0)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)


class Bookmark(Base):
    """书签表：记录用户手动标记的位置。"""
    __tablename__ = "bookmarks"
    id = Column(Integer, primary_key=True, index=True)
    book_id = Column(Integer, index=True, nullable=False)
    chapter_id = Column(Integer, index=True, nullable=True)
    position_ms = Column(Integer, nullable=False, default=0)
    name = Column(String(256), nullable=True)
    note = Column(String(1024), nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)


class Setting(Base):
    """设置表：简单Key-Value。"""
    __tablename__ = "settings"
    id = Column(Integer, primary_key=True, index=True)
    key = Column(String(128), unique=True, nullable=False)
    value_json = Column(String(4096), nullable=True)

