# 用户偏好设置

- 默认技术环境为Obsidian桌面应用，而非浏览器环境

- Obsidian搜索界面偏好设置：1)默认搜索模式OR匹配，适合大多数用户的模糊搜索习惯；2)搜索范围默认"全部内容"，覆盖文件名和内容；3)排序方式默认"相关性"，符合用户期望的智能排序；4)排除目录默认包含.obsidian、Templates、.trash，避免系统文件干扰；5)界面布局采用选项在上、输入框在下的垂直布局，符合Obsidian用户的阅读习惯。
- Obsidian文件管理偏好配置：1)汇总文件默认保存到根目录，避免深层文件夹查找困难；2)文件命名格式"搜索汇总-关键词-日期-时间"，便于时间排序和内容识别；3)自动询问移动到search-results文件夹，给用户选择权而非强制执行；4)新标签页打开文件，保持当前工作状态不被打断；5)临时文件降级策略，确保功能在异常情况下仍可用。
- Obsidian搜索结果显示偏好：1)结果列表采用卡片式布局，提供丰富的文件信息展示；2)显示文件路径、修改时间、文件大小等元数据，帮助用户快速识别；3)内容片段长度限制在100-200字符，平衡信息量和可读性；4)相关性分数显示为小数点后1位，避免过度精确的假象；5)匹配类型标注(filename/content)，让用户了解匹配原因。
- Obsidian交互体验偏好设置：1)搜索防抖延迟设置为300ms，平衡响应速度和性能；2)按钮状态动态显示：有结果时显示导出按钮，无结果时隐藏；3)操作反馈使用Obsidian原生通知样式，保持界面一致性；4)确认对话框仅用于不可逆操作(文件移动/删除)，减少用户打断；5)进度指示器在操作超过1秒时显示，提供明确的状态反馈。
- Obsidian汇总导出格式偏好：1)采用双链格式[[文件路径|文件名]]，符合Obsidian用户的链接习惯；2)汇总结构包含搜索条件、统计信息、结果列表、关联标签四个部分；3)时间格式使用中文本地化显示，提高可读性；4)文件类型统计显示为"类型(数量)"格式，简洁明了；5)关联标签使用双链格式，便于后续的知识图谱连接和主题聚合。
- Obsidian搜索性能偏好配置：1)文件扫描限制：单次处理不超过1000个文件，避免界面卡顿；2)内容读取策略：大文件(>1MB)只读取前10KB内容进行搜索；3)搜索结果数量限制：默认显示前50个最相关结果，提供"加载更多"选项；4)缓存策略：文件元数据缓存30分钟，内容缓存5分钟；5)异步处理：使用Web Worker处理大量文件搜索，保持界面响应性。
- Obsidian用户工作流程适配偏好：1)搜索历史保存最近10次搜索，支持快速重复搜索；2)快捷键支持：Ctrl+Enter执行搜索，Ctrl+E导出汇总，Esc取消搜索；3)自动保存搜索设置：用户的排除目录、搜索模式等偏好自动保存到本地存储；4)集成Obsidian主题：界面颜色和字体跟随用户的主题设置；5)支持多语言：界面文本支持中英文切换，适应不同用户群体。

- 基于实际测试和对比分析的MCP工具选择策略更新：1)寸止MCP最佳适用场景：需要不明确时的智能对话、多方案选择的决策支持、项目规则的动态调整、实时对话流程控制；2)Interactive Feedback MCP最佳适用场景：任务完成后的质量确认、阶段性工作的进展汇报、用户点赞度的主动收集、系统信息的环境检测；3)组合使用策略：寸止MCP处理对话流程，Interactive Feedback MCP处理任务节点，两者功能互补无冲突；4)选择原则：复杂决策场景使用寸止MCP的智能拦截，任务管理场景使用Interactive Feedback MCP的主动反馈
- 用户偏好Windows Terminal配置优化：关注PowerShell外观和可用性改善，喜欢多标签页和分屏功能，重视主题美化和字体配置，希望了解快捷键提高效率，倾向于离线安装方式避免网络依赖，注重安装过程的可控性和故障排除便利性
- 用户建立了标准化英中翻译规则模板：当用户说"翻译 `XXX文件`"时，以下规则自动应用：1) 翻译范围：仅翻译英文文本，保持代码片段、技术术语、文件路径、命令行指令、YAML前置元数据结构不变；2) 翻译质量：确保翻译准确、自然流畅，符合中文表达习惯，保持原意准确性；3) 格式保持：完整保持Markdown格式结构（标题层级、列表、代码块、表格等）；4) 输出要求：保存为`原文件名_zh.md`格式到当前工作目录，使用Markdown格式；5) 工作流程：读取原文件→翻译→保存→确认完成。
- 用户偏好：A 桌面应用（Electron），A1 React+TS+Electron 前端，P1 Python+FastAPI 后端，H1 混合TTS（本地优先，云可选），F1 分章节MP3落地缓存，S1 SQLite存储，W1 Windows优先，V2 多语种多音色可切换；并希望生成总结性Markdown文档，但不要生成测试脚本，且在需要时希望我协助编译和运行。
- 用户确认进入设计阶段，并再次强调：生成总结性Markdown文档；不生成测试脚本；在需要时协助编译与运行。
- 用户要求：先做MVP；生成总结性Markdown文档；不要生成测试脚本；需要在需要时协助编译与运行。
- 用户需要真正的桌面应用EXE，双击就能用，不要开发环境。偏好Python后端+简单前端打包EXE方案，需要生成总结性Markdown文档，需要编译和运行，不要生成测试脚本
