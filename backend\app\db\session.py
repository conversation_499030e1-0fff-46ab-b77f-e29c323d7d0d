# -*- coding: utf-8 -*-
"""数据库会话与引擎配置
- SQLite 数据库默认路径：./temp/db/tts.db（相对项目根）
- 提供 engine 和 SessionLocal（后续依赖注入可用）
"""
from __future__ import annotations
import os
from pathlib import Path

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

# 确保数据库目录存在
DB_PATH = Path(os.getcwd()) / "temp" / "db" / "tts.db"
DB_PATH.parent.mkdir(parents=True, exist_ok=True)

SQLALCHEMY_DATABASE_URL = f"sqlite:///{DB_PATH.as_posix()}"
# check_same_thread=False 允许多线程访问（FastAPI默认每请求线程）
engine = create_engine(SQLALCHEMY_DATABASE_URL, connect_args={"check_same_thread": False})
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

